import { createRouter, createWebHistory } from 'vue-router'
import { get } from '../api/api.js'

function isAuthed(){
  try{ return !!localStorage.getItem('admin') }catch(e){ return false }
}

// 异步检查服务器端登录状态
async function checkServerLoginStatus() {
  try {
    const username = localStorage.getItem('userStatus')
    if (!username) {
      return false
    }

    const response = await get('/admin/checkLogin', { username })
    return response.code === 200 && response.data === true
  } catch (error) {
    console.error('检查登录状态失败:', error)
    return false
  }
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'index',
      component: () => import('../views/index.vue'),
      children:[
        {
          path: '/dashboard',
          name: 'dashboard',
          component: () => import('../views/Dashboard.vue')
        },
        {
          path: '/employee/list',
          name: 'employeeList',
          component: () => import('../views/employee/EmployeeList.vue')
        },
        {
          path: '/department/list',
          name: 'departmentList',
          component: () => import('../views/department/DepartmentList.vue')
        },
        {
          path: '/rose-chart-demo',
          name: 'roseChartDemo',
          component: () => import('../views/RoseChartDemo.vue')
        },
        {
          path:'/upload',
          name: 'upload',
          component: () => import('../views/top/Upload.vue')
        },
        {
          path: '/product/list',
          name: 'productList',
          component: () => import('../views/product/ProductList.vue')
        },
        {
          path: '/product',
          name: 'product',
          component: () => import('../views/product/product.vue')
        }
      ]
    },
      {
      path: '/login',
      name: 'login',
      component: () => import('../views/login.vue')
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/register.vue')
    },
    {
      path: '/forgot-password',
      name: 'forgotPassword',
      component: () => import('../views/admin/ForgotPassword.vue')
    },
    {
      path:'/success',
      name: 'success',
      component: () => import('../views/success.vue')
    }

  ],
})

// 全局路由守卫：检查服务器端登录状态
// router.beforeEach(async (to, from, next) => {
//   const publicPaths = ['/login', '/register', '/forgot-password', '/product/list']

//   // 如果是公开页面，直接放行
//   if (publicPaths.includes(to.path)) {
//     return next()
//   }

//   // 检查本地登录状态
//   const localAuth = isAuthed()
//   if (!localAuth) {
//     // 本地没有登录信息，直接跳转到登录页
//     console.log('本地未登录，跳转到登录页面')
//     return next({ path: '/login', query: { redirect: to.fullPath } })
//   }

//   // 检查服务器端登录状态
//   const isLoggedIn = await checkServerLoginStatus()

//   if (isLoggedIn) {
//     // 用户已登录，允许访问
//     return next()
//   } else {
//     // 用户未登录或会话已过期，清除本地存储并跳转到登录页
//     localStorage.removeItem('userStatus')
//     localStorage.removeItem('admin')
//     localStorage.removeItem('token')
//     console.log('用户会话已过期，跳转到登录页面')
//     return next({ path: '/login', query: { redirect: to.fullPath } })
//   }
// })

export default router
