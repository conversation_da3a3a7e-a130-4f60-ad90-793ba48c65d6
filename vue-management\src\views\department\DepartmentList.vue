<template>
  <div class="department-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>部门管理</span>
          <el-button type="primary" @click="showAddDialog">添加部门</el-button>
        </div>
      </template>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchName"
          placeholder="请输入部门名称"
          style="width: 200px; margin-right: 10px;"
          clearable
        />
        <el-button type="primary" @click="searchDepartments">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </div>

      <!-- 部门表格 -->
      <el-table :data="departmentList" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="部门名称" width="200" />
        <el-table-column prop="description" label="部门描述" />
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button size="small" @click="editDepartment(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteDepartment(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑部门对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑部门' : '添加部门'"
      width="500px"
    >
      <el-form :model="departmentForm" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="部门名称" prop="name">
          <el-input v-model="departmentForm.name" />
        </el-form-item>
        <el-form-item label="部门描述">
          <el-input
            v-model="departmentForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入部门描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveDepartment">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { get, post } from '../../api/api.js'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const departmentList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchName = ref('')
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 部门表单数据
const departmentForm = reactive({
  id: null,
  name: '',
  description: ''
})

// 表单验证规则（根据数据库结构，只有name是必填的）
const rules = {
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 1, max: 100, message: '部门名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 255, message: '部门描述不能超过 255 个字符', trigger: 'blur' }
  ]
}

// 获取部门列表
const getDepartmentList = async () => {
  loading.value = true
  try {
    const response = await get('/department/list', {
      currentPage: currentPage.value,
      pageSize: pageSize.value
    })
    console.log('部门列表响应:', response) // 添加调试日志
    if (response.code === 200) {
      departmentList.value = response.data || []
      total.value = response.total ?? 0
    } else {
      ElMessage.error(response.message || '获取部门列表失败')
      console.error('获取部门列表失败:', response)
    }
  } catch (error) {
    ElMessage.error('获取部门列表失败: ' + error.message)
    console.error('获取部门列表异常:', error)
  } finally {
    loading.value = false
  }
}

// 搜索部门
const searchDepartments = async () => {
  if (!searchName.value.trim()) {
    ElMessage.warning('请输入搜索关键字')
    return
  }
  
  loading.value = true
  try {
    const response = await get('/department/search', { name: searchName.value })
    if (response.code === 200) {
      departmentList.value = response.data
      total.value = response.data.length
    } else {
      ElMessage.error(response.message || '搜索失败')
    }
  } catch (error) {
    ElMessage.error('搜索失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchName.value = ''
  currentPage.value = 1
  getDepartmentList()
}

// 显示添加对话框
const showAddDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑部门
const editDepartment = (department) => {
  isEdit.value = true
  Object.assign(departmentForm, department)
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  Object.assign(departmentForm, {
    id: null,
    name: '',
    description: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 保存部门
const saveDepartment = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const url = isEdit.value ? '/department/update' : '/department/add'
        const response = await get(url, departmentForm)
        
        if (response.code === 200) {
          ElMessage.success(response.message || '操作成功')
          dialogVisible.value = false
          getDepartmentList()
        } else {
          ElMessage.error(response.message || '操作失败')
        }
      } catch (error) {
        ElMessage.error('操作失败')
      }
    }
  })
}

// 删除部门
const deleteDepartment = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除这个部门吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await get('/department/delete', { id })
    if (response.code === 200) {
      ElMessage.success('删除成功')
      getDepartmentList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  getDepartmentList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  getDepartmentList()
}

// 页面加载时获取数据
onMounted(() => {
  getDepartmentList()
})
</script>

<style scoped>
.department-list {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
