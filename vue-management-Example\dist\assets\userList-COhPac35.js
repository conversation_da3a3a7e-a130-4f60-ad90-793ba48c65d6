import{g as w}from"./api-BgzO6Ksg.js";import{r as u,h as S,i as T,c as M,f as d,j as O,b as t,g as h,s as q,a as s,w as o,k as A,l as G,F as H,o as C,e as m,t as y,E as k}from"./index-6989FsBU.js";const J={style:{"margin-bottom":"10px"}},K={style:{display:"flex","align-items":"center"}},Q={style:{"margin-left":"10px"}},R={class:"dialog-footer"},Z={__name:"userList",setup(W){const V=u(!0),i=u(!1),p=u(1),x=u(0),n=S({}),g=u(""),U=a=>{p.value=a,v()},D=async()=>{const a=await w("/selectLikeUsername",{username:g.value});_.value=a.data},E=(a,e)=>{i.value=!0,Object.assign(n,e)},N=()=>{$()},$=async()=>{const a=await w("/updateUser",n);a.code==200?(v(),i.value=!1,k({title:"Success",message:a.message,type:"success"})):k({title:"Error",message:a.message,type:"error"})},B=(a,e)=>{_.value.splice(a,1),console.log(a,e)},_=u([]),v=async()=>{const a=await w("/getUserInfo",{currentPage:p.value});console.log("data===>>",a),_.value=a.data,x.value=a.code,setTimeout(()=>{V.value=!1},300)};return T(async()=>{v()}),(a,e)=>{const c=s("el-input"),r=s("el-button"),f=s("el-table-column"),I=s("el-table"),z=s("el-pagination"),b=s("el-form-item"),P=s("el-form"),j=s("el-dialog"),F=A("loading");return C(),M(H,null,[d("div",J,[t(c,{modelValue:g.value,"onUpdate:modelValue":e[0]||(e[0]=l=>g.value=l),style:{width:"240px"},placeholder:"请输入用户名","prefix-icon":h(q)},null,8,["modelValue","prefix-icon"]),t(r,{type:"danger",onClick:D},{default:o(()=>e[7]||(e[7]=[m("搜索",-1)])),_:1,__:[7]})]),O((C(),G(I,{data:_.value,style:{width:"60%"}},{default:o(()=>[t(f,{label:"用户编号",width:"180"},{default:o(l=>[d("div",K,[d("span",Q,y(l.row.userId),1)])]),_:1}),t(f,{label:"用户名",width:"180"},{default:o(l=>[d("div",null,y(l.row.username),1)]),_:1}),t(f,{label:"邮箱",width:"180"},{default:o(l=>[d("div",null,y(l.row.email),1)]),_:1}),t(f,{label:"操作"},{default:o(l=>[t(r,{size:"small",onClick:L=>E(l.$index,l.row)},{default:o(()=>e[8]||(e[8]=[m(" 编辑 ",-1)])),_:2,__:[8]},1032,["onClick"]),t(r,{size:"small",type:"danger",onClick:L=>B(l.$index,l.row)},{default:o(()=>e[9]||(e[9]=[m(" 删除 ",-1)])),_:2,__:[9]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[F,V.value]]),t(z,{"current-page":p.value,"onUpdate:currentPage":e[1]||(e[1]=l=>p.value=l),"page-size":2,layout:"prev, pager, next",total:x.value,onCurrentChange:U},null,8,["current-page","total"]),t(j,{modelValue:i.value,"onUpdate:modelValue":e[6]||(e[6]=l=>i.value=l),title:"用户修改",width:"500"},{footer:o(()=>[d("div",R,[t(r,{onClick:e[5]||(e[5]=l=>i.value=!1)},{default:o(()=>e[10]||(e[10]=[m("取消",-1)])),_:1,__:[10]}),t(r,{type:"primary",onClick:N},{default:o(()=>e[11]||(e[11]=[m(" 确认 ",-1)])),_:1,__:[11]})])]),default:o(()=>[t(P,{model:n,"label-width":"auto",style:{"max-width":"600px"}},{default:o(()=>[t(b,{label:"用户编号"},{default:o(()=>[t(c,{modelValue:n.userId,"onUpdate:modelValue":e[2]||(e[2]=l=>n.userId=l),disabled:""},null,8,["modelValue"])]),_:1}),t(b,{label:"用户名"},{default:o(()=>[t(c,{modelValue:n.username,"onUpdate:modelValue":e[3]||(e[3]=l=>n.username=l)},null,8,["modelValue"])]),_:1}),t(b,{label:"邮箱"},{default:o(()=>[t(c,{modelValue:n.email,"onUpdate:modelValue":e[4]||(e[4]=l=>n.email=l)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])],64)}}};export{Z as default};
