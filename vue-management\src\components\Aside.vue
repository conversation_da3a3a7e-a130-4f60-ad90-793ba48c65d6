<template>
  <div class="simple-sidebar">
    <el-menu
      :default-active="currentRouter"
      class="sidebar-menu"
      @select="handleSelect"
      router
    >
      <el-menu-item index="/dashboard">
        <el-icon><HomeFilled /></el-icon>
        <span>系统概览</span>
      </el-menu-item>

      <el-menu-item index="/employee/list">
        <el-icon><UserFilled /></el-icon>
        <span>员工管理</span>
      </el-menu-item>

      <el-menu-item index="/department/list">
        <el-icon><OfficeBuilding /></el-icon>
        <span>部门管理</span>
      </el-menu-item>

      <el-menu-item index="/product/list">
        <el-icon><ShoppingBag /></el-icon>
        <span>商品管理</span>
      </el-menu-item>

      <el-menu-item index="/rose-chart-demo">
        <el-icon><PieChart /></el-icon>
        <span>玫瑰图演示</span>
      </el-menu-item>

      <el-menu-item index="/product">
        <el-icon><Upload /></el-icon>
        <span>商品购买</span>
      </el-menu-item>
    </el-menu>

    <!-- 集成的玫瑰图组件 -->
    <div v-if="showRoseChart" class="rose-chart-container">
      <div class="chart-header" v-if="chartTitle">
        <h3>{{ chartTitle }}</h3>
      </div>
      <div ref="chartRef" :style="{ width: chartWidth, height: chartHeight }" class="rose-chart"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
/**
 * Integrated Sidebar Navigation Component
 * @description 集成了导航菜单和玫瑰图功能的侧边栏组件
 */

import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import * as echarts from 'echarts'

const router = useRouter()
const route = useRoute()
const currentRouter = ref("")

// 玫瑰图相关数据
const showRoseChart = ref(false)
const chartTitle = ref('数据统计')
const chartWidth = ref('100%')
const chartHeight = ref('300px')
const chartRef = ref(null)
let chartInstance = null

// 默认图表数据
const defaultChartData = [
  { id: 1, label: '员工', count: 40, color: '#FF6B6B' },
  { id: 2, label: '部门', count: 38, color: '#FFD93D' },
  { id: 3, label: '商品', count: 32, color: '#6BCB77' },
  { id: 4, label: '订单', count: 30, color: '#4D96FF' }
]

// Handle menu item selection
const handleSelect = (index: string) => {
  router.push(index)
}

// Set current active route
onMounted(() => {
  currentRouter.value = route.path
})

// 玫瑰图相关方法
const initChart = async () => {
  if (!showRoseChart.value) return

  await nextTick()

  if (!chartRef.value) return

  // 销毁已存在的图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 创建新的图表实例
  chartInstance = echarts.init(chartRef.value)

  // 设置图表配置
  updateChart()
}

const updateChart = () => {
  if (!chartInstance || !defaultChartData || defaultChartData.length === 0) return

  // 处理数据
  const chartData = defaultChartData.map(item => ({
    name: item.label,
    value: item.count,
    itemStyle: {
      color: item.color || '#5470c6'
    }
  }))

  // ECharts配置选项
  const option = {
    title: {
      text: chartTitle.value,
      left: 'center',
      top: 10,
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: chartTitle.value,
        type: 'pie',
        radius: ['20%', '70%'],
        center: ['50%', '60%'],
        roseType: 'radius',
        data: chartData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  // 设置图表选项
  chartInstance.setOption(option, true)
}

// 切换玫瑰图显示
const toggleRoseChart = () => {
  showRoseChart.value = !showRoseChart.value
  if (showRoseChart.value) {
    nextTick(() => {
      initChart()
    })
  }
}

// 窗口大小变化时重新调整图表
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})

// 暴露方法
defineExpose({
  toggleRoseChart,
  getChartInstance: () => chartInstance,
  refresh: updateChart,
  resize: handleResize
})
</script>

<style scoped>
.simple-sidebar {
  height: 100%;
  background: #fff;
}

.sidebar-menu {
  border-right: none;
  background: #fff;
}

:deep(.el-menu-item) {
  color: #333;
  padding: 0 20px;
  height: 50px;
  line-height: 50px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-menu-item:hover) {
  background-color: #f5f5f5;
  color: #409eff;
}

:deep(.el-menu-item.is-active) {
  background-color: #409eff;
  color: #fff;
}

:deep(.el-menu-item .el-icon) {
  margin-right: 8px;
  font-size: 16px;
}

/* 玫瑰图样式 */
.rose-chart-container {
  margin-top: 20px;
  padding: 10px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  text-align: center;
  margin-bottom: 10px;
}

.chart-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.rose-chart {
  background: transparent;
}
</style>