<template>
  <div class="container">
	  <el-row class="tac">
	    <el-col :span="24">
	      <el-menu
	        active-text-color="#ffd04b"
	        background-color="#545c64"
	        class="el-menu-vertical-demo"
	        :default-active="currentRouter"
	        text-color="#fff"
	        @open="handleOpen"
	        @close="handleClose"
			@select="handleSelect"
			router
	      >
	        <el-sub-menu index="1">
	          <template #title>
				<el-icon><HomeFilled /></el-icon>
	            <span>首页</span>
	          </template>
	            <el-menu-item index="/dashboard">系统概览</el-menu-item>
	        </el-sub-menu>


			<el-sub-menu index="2">
			  <template #title>
			    <el-icon><UserFilled /></el-icon>
			    <span>员工管理</span>
			  </template>
			    <el-menu-item index="/employee/list">员工列表</el-menu-item>
			</el-sub-menu>


			<el-sub-menu index="3">
			  <template #title>
			    <el-icon><location /></el-icon>
			    <span>部门管理</span>
			  </template>
			    <el-menu-item index="/department/list">部门列表</el-menu-item>
			</el-sub-menu>





	      </el-menu>
		  
	    </el-col>
	  </el-row>
  </div>
</template>

<style>
	
	.el-menu{
		border-right: 0;
	}
	
</style>

<script lang="ts" setup>
	import {ref} from 'vue';
	import { useRouter } from 'vue-router';
	const router = useRouter();
const handleOpen = (key: string, keyPath: string[]) => {

}
const handleClose = (key: string, keyPath: string[]) => {
  
}

const handleSelect=(index)=>{
	 router.push(index);
}


const currentRouter=ref("");
</script>