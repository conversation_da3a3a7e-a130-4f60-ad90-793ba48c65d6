<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="qidian.it.springbootalipay.mapper.ProductMapper" >
  <resultMap id="BaseResultMap" type="qidian.it.springbootalipay.entity.Product" >
    <id column="product_id" property="productId" jdbcType="INTEGER" />
    <result column="product_name" property="productName" jdbcType="VARCHAR" />
    <result column="price" property="price" jdbcType="DOUBLE" />
    <result column="storage_num" property="storageNum" jdbcType="INTEGER" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="product_images" property="productImages" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    product_id, product_name, price, storage_num, description, product_images
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from product
    where product_id = #{productId,jdbcType=INTEGER}
  </select>

    <select id="selectAllProduct" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from product
    </select>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from product
    where product_id = #{productId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="qidian.it.springbootalipay.entity.Product" >
    insert into product (product_id, product_name, price, 
      storage_num, description, product_images
      )
    values (#{productId,jdbcType=INTEGER}, #{productName,jdbcType=VARCHAR}, #{price,jdbcType=DOUBLE}, 
      #{storageNum,jdbcType=INTEGER}, #{description,jdbcType=VARCHAR}, #{productImages,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="qidian.it.springbootalipay.entity.Product" >
    insert into product
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="productId != null" >
        product_id,
      </if>
      <if test="productName != null" >
        product_name,
      </if>
      <if test="price != null" >
        price,
      </if>
      <if test="storageNum != null" >
        storage_num,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="productImages != null" >
        product_images,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="productId != null" >
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="productName != null" >
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        #{price,jdbcType=DOUBLE},
      </if>
      <if test="storageNum != null" >
        #{storageNum,jdbcType=INTEGER},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="productImages != null" >
        #{productImages,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="qidian.it.springbootalipay.entity.Product" >
    update product
    <set >
      <if test="productName != null" >
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        price = #{price,jdbcType=DOUBLE},
      </if>
      <if test="storageNum != null" >
        storage_num = #{storageNum,jdbcType=INTEGER},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="productImages != null" >
        product_images = #{productImages,jdbcType=VARCHAR},
      </if>
    </set>
    where product_id = #{productId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="qidian.it.springbootalipay.entity.Product" >
    update product
    set product_name = #{productName,jdbcType=VARCHAR},
      price = #{price,jdbcType=DOUBLE},
      storage_num = #{storageNum,jdbcType=INTEGER},
      description = #{description,jdbcType=VARCHAR},
      product_images = #{productImages,jdbcType=VARCHAR}
    where product_id = #{productId,jdbcType=INTEGER}
  </update>
</mapper>