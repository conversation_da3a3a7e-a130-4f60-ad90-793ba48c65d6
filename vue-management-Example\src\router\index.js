import { createRouter, createWebHistory } from 'vue-router'
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'index',
      component: () => import('../views/index.vue'),
      children:[
        {
          path: '/userList',
          name: 'userList',
          component: () => import('../views/user/userList.vue')
        },
         {
          path: '/empList',
          name: 'empList',
          component: () => import('../views/emp/empList.vue')
        },
        {
          path: '/deptList',
          name: 'deptList',
          component: () => import('../views/dept/deptList.vue')
        },
         {
          path: '/BigData',
          name: 'BigData',
          component: () => import('../views/top/BigData.vue')
        },
         {
          path: '/upload',
          name: 'upload',
          component: () => import('../views/top/upload.vue')
        },
         {
          path: '/product',
          name: 'product',
          component: () => import('../views/product/product.vue')
        },
        
      ]
    },
      {
      path: '/login',
      name: 'login',
      component: () => import('../views/login.vue')
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/register.vue')
    },
     {
      path: '/success',
      name: 'success',
      component: () => import('../views/success.vue')
    },
  ],
})

export default router
