package qidian.it.mybatisplus.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.CrossOrigin;
import qidian.it.mybatisplus.entity.Admin;
import qidian.it.mybatisplus.service.IAdminService;
import qidian.it.mybatisplus.util.Result;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@RestController
@RequestMapping("/admin")
@CrossOrigin(origins = "*")
public class AdminController {

    @Autowired
    private IAdminService adminService;

    @RequestMapping("/selectByName")
    public Admin selectByName(String name){
        return adminService.selectByName( name);
    }

    @RequestMapping("/selectAll")
    public List< Admin> selectAll(
            @RequestParam(defaultValue = "1") long current){
        return adminService.selectAll(current);
    }
    @RequestMapping(value = "/register", method = {RequestMethod.GET, RequestMethod.POST})
    public Result register(@RequestBody(required = false) Admin admin,
                          String username, String password, String email){
        // 如果是JSON请求，使用@RequestBody的admin对象
        if (admin != null && admin.getUsername() != null) {
            return adminService.register(admin);
        }
        // 如果是表单请求，手动构建admin对象
        else if (username != null && password != null) {
            Admin newAdmin = new Admin();
            newAdmin.setUsername(username);
            newAdmin.setPassword(password);
            newAdmin.setEmail(email);
            return adminService.register(newAdmin);
        }
        else {
            return Result.fail("请提供用户名和密码");
        }
    }

    @RequestMapping(value = "/login", method = {RequestMethod.GET, RequestMethod.POST})
    public Result login(String username, String password){
        return adminService.login(username, password);
    }

    @RequestMapping("/checkLogin")
    public Result checkLogin(String username){
        return adminService.checkLogin(username);
    }
}
