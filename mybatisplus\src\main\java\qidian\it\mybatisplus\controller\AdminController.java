package qidian.it.mybatisplus.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import qidian.it.mybatisplus.entity.Admin;
import qidian.it.mybatisplus.service.IAdminService;
import qidian.it.mybatisplus.util.Result;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@RestController
@RequestMapping("/admin")
public class AdminController {

    @Autowired
    private IAdminService adminService;

    @RequestMapping("/selectByName")
    public Admin selectByName(String name){
        return adminService.selectByName( name);
    }

    @RequestMapping("/selectAll")
    public List< Admin> selectAll(
            @RequestParam(defaultValue = "1") long current){
        return adminService.selectAll(current);
    }
    @RequestMapping("/register")
    public Result register(Admin admin){
        return adminService.register(admin);
    }

    @RequestMapping("/login")
    public Result login(String username, String password){
        return adminService.login(username, password);
    }
}
