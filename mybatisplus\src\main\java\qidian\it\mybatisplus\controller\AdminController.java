package qidian.it.mybatisplus.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.CrossOrigin;
import qidian.it.mybatisplus.config.JwtConfig;
import qidian.it.mybatisplus.annotation.RequiredToken;
import qidian.it.mybatisplus.entity.Admin;
import qidian.it.mybatisplus.service.IAdminService;
import qidian.it.mybatisplus.util.Result;
import qidian.it.mybatisplus.util.EmailUtil;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@RestController
@RequestMapping("/admin")
@CrossOrigin(origins = "*")
public class AdminController {

    @Autowired
    private IAdminService adminService;

    @Autowired
    private JwtConfig jwtConfig;

    @RequestMapping("/selectByName")
    public Admin selectByName(String name){
        return adminService.selectByName( name);
    }

    @RequestMapping("/selectAll")
    @RequiredToken
    public List< Admin> selectAll(
            @RequestParam(defaultValue = "1") long current){
        return adminService.selectAll(current);
    }
    @RequestMapping(value = "/register", method = {RequestMethod.GET, RequestMethod.POST})
    public Result register(@RequestBody(required = false) Admin admin,
                          String username, String password, String email){
        // 如果是JSON请求，使用@RequestBody的admin对象
        if (admin != null && admin.getUsername() != null) {
            return adminService.register(admin);
        }
        // 如果是表单请求，手动构建admin对象
        else if (username != null && password != null) {
            Admin newAdmin = new Admin();
            newAdmin.setUsername(username);
            newAdmin.setPassword(password);
            newAdmin.setEmail(email);
            return adminService.register(newAdmin);
        }
        else {
            return Result.fail("请提供用户名和密码");
        }
    }

    @RequestMapping(value = "/login", method = {RequestMethod.GET, RequestMethod.POST})
    public Result login(String username, String password){
        Result loginResult = adminService.login(username, password);

        // 如果登录成功，生成JWT token
        if (loginResult.getCode() == 200) {
            String token = jwtConfig.createToken(username);

            // 将token添加到返回数据中
            if (loginResult.getData() instanceof Admin) {
                Admin admin = (Admin) loginResult.getData();
                // 创建包含token的响应数据
                java.util.Map<String, Object> responseData = new java.util.HashMap<>();
                responseData.put("admin", admin);
                responseData.put("token", token);

                return Result.success("登录成功", responseData);
            }
        }

        return loginResult;
    }

    @RequestMapping("/checkLogin")
    @RequiredToken
    public Result checkLogin(String username){
        return adminService.checkLogin(username);
    }

    @RequestMapping("/getCode")
    public Result getCode(String username, String email){
        String code = EmailUtil.sendMail(email);
        // 这里应该将验证码存储到Redis或内存中，暂时返回验证码用于测试
        // 在实际项目中，不应该直接返回验证码
        return Result.success(code);
    }

    @RequestMapping("/checkCodeExpire")
    public Result checkCodeExpire(String username){
        // 这里应该检查Redis中的验证码是否过期
        // 暂时返回验证成功，实际项目中需要Redis支持
        return Result.success("验证成功");
    }
}
