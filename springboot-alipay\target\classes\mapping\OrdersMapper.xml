<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="qidian.it.springbootalipay.mapper.OrdersMapper" >
  <resultMap id="BaseResultMap" type="qidian.it.springbootalipay.entity.Orders" >
    <id column="order_id" property="orderId" jdbcType="INTEGER" />
    <result column="user_id" property="userId" jdbcType="INTEGER" />
    <result column="total_price" property="totalPrice" jdbcType="DOUBLE" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="out_trade_no" property="outTradeNo" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    order_id, user_id, total_price, status, create_time, out_trade_no
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from orders
    where order_id = #{orderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from orders
    where order_id = #{orderId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="qidian.it.springbootalipay.entity.Orders" >
    insert into orders (order_id, user_id, total_price,
                        status, create_time, out_trade_no
    )
    values (#{orderId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, #{totalPrice,jdbcType=DOUBLE},
            #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{outTradeNo,jdbcType=VARCHAR}
           )
  </insert>
  <insert id="insertSelective" parameterType="qidian.it.springbootalipay.entity.Orders"  useGeneratedKeys="true" keyProperty="orderId">
    insert into orders
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="orderId != null" >
        order_id,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="totalPrice != null" >
        total_price,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="outTradeNo != null" >
        out_trade_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="orderId != null" >
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null" >
        #{totalPrice,jdbcType=DOUBLE},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outTradeNo != null" >
        #{outTradeNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="qidian.it.springbootalipay.entity.Orders" >
    update orders
    <set >
      <if test="userId != null" >
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null" >
        total_price = #{totalPrice,jdbcType=DOUBLE},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outTradeNo != null" >
        out_trade_no = #{outTradeNo,jdbcType=VARCHAR},
      </if>
    </set>
    where order_id = #{orderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="qidian.it.springbootalipay.entity.Orders" >
    update orders
    set user_id = #{userId,jdbcType=INTEGER},
        total_price = #{totalPrice,jdbcType=DOUBLE},
        status = #{status,jdbcType=INTEGER},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        out_trade_no = #{outTradeNo,jdbcType=VARCHAR}
    where order_id = #{orderId,jdbcType=INTEGER}
  </update>
</mapper>