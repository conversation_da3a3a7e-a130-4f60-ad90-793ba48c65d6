import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
const app = createApp(App)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
app.use(router)
app.use(ElementPlus)

// 页面关闭/刷新自动登出（清理本地登录态）
window.addEventListener('beforeunload', () => {
  try{ localStorage.removeItem('admin') }catch(e){}
})

app.mount('#app')
