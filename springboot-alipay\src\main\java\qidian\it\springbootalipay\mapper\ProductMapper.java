package qidian.it.springbootalipay.mapper;

import qidian.it.springbootalipay.entity.Product;

import java.util.List;

public interface ProductMapper {
    int deleteByPrimaryKey(Integer productId);

    int insert(Product record);

    int insertSelective(Product record);

    Product selectByPrimaryKey(Integer productId);


    List<Product> selectAllProduct();

    int updateByPrimaryKeySelective(Product record);

    int updateByPrimaryKey(Product record);
}