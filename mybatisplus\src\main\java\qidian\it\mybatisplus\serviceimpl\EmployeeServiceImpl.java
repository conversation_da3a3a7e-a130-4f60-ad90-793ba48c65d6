package qidian.it.mybatisplus.serviceimpl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import qidian.it.mybatisplus.entity.Employee;
import qidian.it.mybatisplus.mapper.EmployeeMapper;
import qidian.it.mybatisplus.service.IEmployeeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import qidian.it.mybatisplus.util.Result;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Service
public class EmployeeServiceImpl extends ServiceImpl<EmployeeMapper, Employee> implements IEmployeeService {

    @Override
    public void exportData(HttpServletResponse resp) {
        List emplist = baseMapper.selectList(new LambdaQueryWrapper<>());
        System.out.println(emplist);
        // 设置响应头
        resp.setContentType("application/vnd.ms-excel");
        // 设置响应字符编码
        resp.setCharacterEncoding("utf-8");
        // 设置响应头
        resp.setHeader("Content-disposition", "attachment;filename=employees.xlsx");
        try {
            EasyExcel.write(resp.getOutputStream(), Employee.class).sheet("员工信息").doWrite(emplist);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Result invalid(String username) {
        return null;
    }
}
