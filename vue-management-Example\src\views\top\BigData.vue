<template>
<div ref="chartContainer" class="chart"></div>

<div ref="chartContainer2" class="chart"></div>

</template>

<script setup>
import * as echarts from 'echarts';
import {ref,onMounted} from 'vue'

//模拟后端响应的数据
const arr=ref([
    {day:'星期一',value:100},
    {day:'星期二',value:50},
    {day:'星期三',value:30},
    {day:'星期四',value:40},
    {day:'星期五',value:60},
    {day:'星期六',value:80},
    {day:'星期日',value:90}
]);

const x2=ref([]);
const y2=ref([]);




const x=ref(['星期一','星期二','星期三','星期四','星期五','星期六','星期日'])
const y=ref([100,50,30,40,500,1600,120]);
const chartContainer=ref(null);
const chartContainer2=ref(null);

	const initChart=()=>{
	 const myChart = echarts.init(chartContainer.value);
	 const option = {
            xAxis: {
                type: 'category',
                data: x.value
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                data: y.value,
                type: 'line'
                }
            ]
            };
	      myChart.setOption(option);
	}





    const getData=()=>{
        arr.value.forEach(item=>{
            x2.value.push(item.day)
            y2.value.push(item.value)
        })
    }


    const initChart2=()=>{
     const myChart = echarts.init(chartContainer2.value);
	 const option = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: x2.value,
      axisTick: {
        alignWithLabel: true
      }
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: [
    {
      name: 'Direct',
      type: 'bar',
      barWidth: '60%',
      data: y2.value
    }
  ]
};
	      myChart.setOption(option);
    }

onMounted(()=>{
    initChart();
    getData();
    initChart2();

})



</script>

<style scoped>
	.chart {
	  width: 40%;
	  height: 400px;
	}
</style>