server:
  port: 8082

spring:
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************************************************
    username: root
    password: qwer2690682044
    hikari:
      connection-timeout: 6000
      maximum-pool-size: 5
#  jackson:
#    date-format: yyyy-MM-dd HH:mm:ss
#    time-zone: GMT+8
#    serialization:
#      write-dates-as-timestamps: false

mybatis-plus:
  configuration:
    #开启驼峰功能,数据库字段hello_world 实体类helloWolrd 也能对应匹配
    map-underscore-to-camel-case: true
    #结果集自动映射(resultMap)
    auto-mapping-behavior: full
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:mapping/*Mapper.xml
  global-config:
    # 逻辑删除配置
    db-config:
      # 删除前
      logic-not-delete-value: 1
      # 删除后
      logic-delete-value: 0

config:
  jwt:
    # 加密密钥
    secret: abcdefg1234213213123123123123123123123213123123125gdfgdffdgfgdfdggfd23123123214214141231233332212121222222222222211122222222222222222222222222222222222222222222222222222222222222222222222222222222221111111111sadasdasdasdasdasdsadsadasdsadsadasdasdasdasdsad
    # token有效时长
    expire: 10000
    # header 名称
    header: token