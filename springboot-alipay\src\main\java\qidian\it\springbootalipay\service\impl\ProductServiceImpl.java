package qidian.it.springbootalipay.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qidian.it.springbootalipay.entity.Result;
import qidian.it.springbootalipay.mapper.ProductMapper;
import qidian.it.springbootalipay.service.ProductService;


@Service
public class ProductServiceImpl implements ProductService {
@Autowired
private ProductMapper productMapper;

    @Override
    public Result getProductList() {
        return Result.success(productMapper.selectAllProduct());
    }

    @Override
    public Result getProductById(Integer productId) {
        return Result.success(productMapper.selectByPrimaryKey(productId));
    }
}
