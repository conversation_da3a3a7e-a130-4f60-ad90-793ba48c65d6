package qidian.it.mybatisplus.service;

import qidian.it.mybatisplus.entity.Admin;
import com.baomidou.mybatisplus.extension.service.IService;
import qidian.it.mybatisplus.util.Result;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
public interface IAdminService extends IService<Admin> {

   Admin selectByName(String name);

   List< Admin> selectAll(long current);

   Result register(Admin admin);

   Result login(String username, String password);

   Result checkLogin(String username);
}
