# 项目启动指南

## 问题解决方案

### 1. 强制登录功能已启用 ✅
- 前端路由守卫已启用，未登录用户会被重定向到登录页面
- 使用后端创建的登录接口进行身份验证

### 2. 跨域问题已解决 ✅
- 已创建 `CorsConfig.java` 配置类
- 允许前端Vue项目（localhost:5173）访问后端接口（localhost:8082）

### 3. 后端登录接口已完善 ✅
- 新增 `/admin/login` 登录接口
- 新增 `/admin/checkLogin` 登录状态检查接口
- 支持用户名密码验证

## 启动步骤

### 第一步：准备数据库
1. 确保MySQL数据库服务已启动
2. 执行数据库初始化脚本：
   ```sql
   -- 运行 数据库初始化脚本.sql 文件
   source 数据库初始化脚本.sql
   ```
3. 验证数据库连接配置（application.yml）：
   ```yaml
   spring:
     datasource:
       url: *********************************
       username: root
       password: qwer2690682044  # 请确认密码正确
   ```

### 第二步：启动后端项目
1. 进入后端项目目录：
   ```bash
   cd mybatisplus
   ```
2. 编译并启动Spring Boot应用：
   ```bash
   # 使用Maven启动
   mvn spring-boot:run
   
   # 或者在IDE中直接运行 MybatisplusApplication.java
   ```
3. 验证后端启动成功：
   - 访问：http://localhost:8082/admin/selectAll
   - 应该返回JSON数据或错误信息

### 第三步：启动前端项目
1. 进入前端项目目录：
   ```bash
   cd vue-management
   ```
2. 安装依赖（如果还没安装）：
   ```bash
   npm install
   ```
3. 启动开发服务器：
   ```bash
   npm run dev
   ```
4. 访问前端应用：http://localhost:5173

## 测试登录功能

### 可用的测试账号：
- **用户名**: admin, **密码**: 123456
- **用户名**: test, **密码**: test123  
- **用户名**: user1, **密码**: password

### 登录流程测试：
1. 访问 http://localhost:5173
2. 由于强制登录功能已启用，会自动跳转到登录页面
3. 使用上述测试账号登录
4. 登录成功后会跳转到系统首页

## 常见问题排查

### 1. 前端连不上后端
**症状**: 前端显示网络错误，控制台显示CORS错误或连接失败

**解决方案**:
- 确认后端服务已启动（端口8082）
- 检查防火墙设置
- 验证跨域配置是否生效

**验证方法**:
```bash
# 测试后端接口是否可访问
curl http://localhost:8082/admin/selectAll
```

### 2. 数据库连接失败
**症状**: 后端启动时报数据库连接错误

**解决方案**:
- 确认MySQL服务已启动
- 检查数据库用户名密码是否正确
- 确认数据库 `basic` 已创建
- 检查MySQL端口（默认3306）是否可访问

**验证方法**:
```bash
# 测试数据库连接
mysql -u root -p -h localhost -P 3306
use basic;
show tables;
```

### 3. 登录验证失败
**症状**: 输入正确用户名密码但登录失败

**解决方案**:
- 确认测试用户已插入数据库
- 检查后端日志是否有错误信息
- 验证登录接口是否正常工作

**验证方法**:
```bash
# 直接测试登录接口
curl -X POST http://localhost:8082/admin/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=123456"
```

## 接口文档

### 登录接口
- **URL**: POST /admin/login
- **参数**: username, password (表单格式)
- **返回**: 
  ```json
  {
    "code": 200,
    "message": "登录成功",
    "data": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>"
    }
  }
  ```

### 登录状态检查接口
- **URL**: GET /admin/checkLogin?username=admin
- **返回**:
  ```json
  {
    "code": 200,
    "message": "用户已登录",
    "data": true
  }
  ```

## 项目结构
```
项目根目录/
├── mybatisplus/                 # 后端Spring Boot项目
│   ├── src/main/java/
│   │   └── qidian/it/mybatisplus/
│   │       ├── config/
│   │       │   ├── CorsConfig.java      # 跨域配置
│   │       │   └── MybatisPlusConfig.java
│   │       ├── controller/
│   │       │   └── AdminController.java  # 登录接口
│   │       ├── service/
│   │       └── entity/
│   └── src/main/resources/
│       └── application.yml              # 数据库配置
├── vue-management/              # 前端Vue项目
│   ├── src/
│   │   ├── api/api.js          # API请求配置
│   │   ├── router/index.js     # 路由配置（强制登录）
│   │   └── views/login.vue     # 登录页面
│   └── package.json
├── 数据库初始化脚本.sql          # 数据库初始化
└── 启动指南.md                  # 本文件
```

按照以上步骤操作，应该可以解决前端连不上后端和数据库数据获取失败的问题。
