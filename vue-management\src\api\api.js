/**
 * HTTP Request Service Module
 * <AUTHOR>
 * @version 2.0.0
 * @description Centralized API communication layer with enhanced error handling
 */

import axios from 'axios'

// Configuration constants
const API_CONFIG = {
  BASE_URL: 'http://localhost:8082',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3
}

/**
 * Create axios instance with predefined configuration
 */
const httpClient = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json'
  }
})

/**
 * Request interceptor - Auto-attach authentication headers
 */
httpClient.interceptors.request.use(
  (requestConfig) => {
    const userSession = localStorage.getItem('admin')

    if (userSession) {
      try {
        const { username } = JSON.parse(userSession)
        // Attach user context for potential backend extensions
        requestConfig.headers['X-Current-User'] = username || 'anonymous'
      } catch (parseError) {
        console.warn('Session parsing failed:', parseError)
      }
    }

    return requestConfig
  },
  (requestError) => {
    console.error('Request configuration error:', requestError)
    return Promise.reject(requestError)
  }
)

/**
 * Response interceptor - Standardize error handling
 */
httpClient.interceptors.response.use(
  (response) => response,
  (responseError) => {
    const errorMessage = responseError.response?.data?.message || responseError.message
    console.error('API Response Error:', errorMessage)
    return Promise.reject(responseError)
  }
)

/**
 * Execute GET request with query parameters
 * @param {string} endpoint - API endpoint path
 * @param {Object} queryParams - URL query parameters
 * @returns {Promise<Object>} Response data
 */
export const fetchData = async (endpoint, queryParams = {}) => {
  try {
    const response = await httpClient.get(endpoint, { params: queryParams })
    return response.data
  } catch (requestError) {
    console.error(`GET ${endpoint} failed:`, requestError)
    throw new Error(`Data fetch failed: ${requestError.message}`)
  }
}

/**
 * Execute POST request with payload
 * @param {string} endpoint - API endpoint path
 * @param {Object} payload - Request body data
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} Response data
 */
export const submitData = async (endpoint, payload, options = {}) => {
  try {
    const response = await httpClient.post(endpoint, payload, options)
    return response.data
  } catch (requestError) {
    console.error(`POST ${endpoint} failed:`, requestError)
    throw new Error(`Data submission failed: ${requestError.message}`)
  }
}

/**
 * Execute PUT request for updates
 * @param {string} endpoint - API endpoint path
 * @param {Object} payload - Update data
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} Response data
 */
export const updateData = async (endpoint, payload, options = {}) => {
  try {
    const response = await httpClient.put(endpoint, payload, options)
    return response.data
  } catch (requestError) {
    console.error(`PUT ${endpoint} failed:`, requestError)
    throw new Error(`Data update failed: ${requestError.message}`)
  }
}

/**
 * Execute DELETE request
 * @param {string} endpoint - API endpoint path
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} Response data
 */
export const removeData = async (endpoint, options = {}) => {
  try {
    const response = await httpClient.delete(endpoint, options)
    return response.data
  } catch (requestError) {
    console.error(`DELETE ${endpoint} failed:`, requestError)
    throw new Error(`Data removal failed: ${requestError.message}`)
  }
}


// Legacy compatibility exports
export const get = fetchData
export const post = submitData
export const put = updateData
export const del = removeData

export default httpClient