package qidian.it.mybatisplus.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @company 起点编程
 * @Date: 2025/4/11 10:29
 * @Description 自定义注解
 */
@Target({ ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequiredToken {
    //注解功能:方法上加了该注解，需要验证请求的token
}
