<template>
  <div class="rose-chart-demo">
    <div class="demo-header">
      <h1>ECharts 玫瑰图组件演示</h1>
      <p>基于提供的数组数据创建的玫瑰图组件，支持多种配置选项</p>
    </div>

    <!-- 控制面板 -->
    <el-card class="control-panel" shadow="hover">
      <template #header>
        <span>配置选项</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="图表标题">
            <el-input v-model="chartTitle" placeholder="请输入图表标题" />
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item label="玫瑰图类型">
            <el-select v-model="roseType" placeholder="选择类型">
              <el-option label="半径模式" value="radius" />
              <el-option label="面积模式" value="area" />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item label="图例位置">
            <el-select v-model="legendPosition" placeholder="选择位置">
              <el-option label="右侧" value="right" />
              <el-option label="左侧" value="left" />
              <el-option label="顶部" value="top" />
              <el-option label="底部" value="bottom" />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item label="显示图例">
            <el-switch v-model="showLegend" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>

    <!-- 图表展示区域 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 默认数据图表 -->
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <span>默认数据玫瑰图</span>
          </template>
          <RoseChart
            :data="defaultData"
            :title="chartTitle"
            :rose-type="roseType"
            :legend-position="legendPosition"
            :show-legend="showLegend"
            height="350px"
          />
        </el-card>
      </el-col>

      <!-- 自定义数据图表 -->
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <span>自定义数据玫瑰图</span>
          </template>
          <RoseChart
            :data="customData"
            title="销售数据分析"
            rose-type="area"
            legend-position="bottom"
            height="350px"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据展示 -->
    <el-card style="margin-top: 20px;" shadow="hover">
      <template #header>
        <span>原始数据</span>
      </template>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="默认数据" name="default">
          <el-table :data="defaultData" style="width: 100%">
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="label" label="标签" />
            <el-table-column prop="count" label="数量" />
            <el-table-column prop="color" label="颜色">
              <template #default="scope">
                <div class="color-preview">
                  <span 
                    class="color-block" 
                    :style="{ backgroundColor: scope.row.color }"
                  ></span>
                  {{ scope.row.color }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="自定义数据" name="custom">
          <el-table :data="customData" style="width: 100%">
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="label" label="标签" />
            <el-table-column prop="count" label="数量" />
            <el-table-column prop="color" label="颜色">
              <template #default="scope">
                <div class="color-preview">
                  <span 
                    class="color-block" 
                    :style="{ backgroundColor: scope.row.color }"
                  ></span>
                  {{ scope.row.color }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 使用说明 -->
    <el-card style="margin-top: 20px;" shadow="hover">
      <template #header>
        <span>组件使用说明</span>
      </template>
      
      <div class="usage-guide">
        <h4>基本用法：</h4>
        <pre><code>&lt;RoseChart 
  :data="chartData" 
  title="我的玫瑰图"
  rose-type="radius"
  legend-position="right"
  :show-legend="true"
  height="400px"
/&gt;</code></pre>

        <h4>Props 说明：</h4>
        <ul>
          <li><strong>data</strong>: 图表数据数组，每个对象包含 id, label, count, color 字段</li>
          <li><strong>title</strong>: 图表标题</li>
          <li><strong>width</strong>: 图表宽度，默认 '100%'</li>
          <li><strong>height</strong>: 图表高度，默认 '400px'</li>
          <li><strong>showLegend</strong>: 是否显示图例，默认 true</li>
          <li><strong>legendPosition</strong>: 图例位置，可选 'left', 'right', 'top', 'bottom'</li>
          <li><strong>roseType</strong>: 玫瑰图类型，可选 'radius' 或 'area'</li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import RoseChart from '@/components/RoseChart.vue'

// 响应式数据
const chartTitle = ref('数据分布玫瑰图')
const roseType = ref('radius')
const legendPosition = ref('right')
const showLegend = ref(true)
const activeTab = ref('default')

// 题目提供的默认数据
const defaultData = ref([
  { id: 1, label: 'rose 1', count: 40, color: '#FF6B6B' },
  { id: 2, label: 'rose 2', count: 38, color: '#FFD93D' },
  { id: 3, label: 'rose 3', count: 32, color: '#6BCB77' },
  { id: 4, label: 'rose 4', count: 30, color: '#4D96FF' },
  { id: 5, label: 'rose 5', count: 28, color: '#B983FF' },
  { id: 6, label: 'rose 6', count: 26, color: '#FF9F1C' },
  { id: 7, label: 'rose 7', count: 22, color: '#8338EC' },
  { id: 8, label: 'rose 8', count: 18, color: '#3A86FF' }
])

// 自定义数据示例
const customData = ref([
  { id: 1, label: '产品A', count: 120, color: '#FF6B6B' },
  { id: 2, label: '产品B', count: 98, color: '#4ECDC4' },
  { id: 3, label: '产品C', count: 86, color: '#45B7D1' },
  { id: 4, label: '产品D', count: 72, color: '#96CEB4' },
  { id: 5, label: '产品E', count: 65, color: '#FFEAA7' },
  { id: 6, label: '产品F', count: 54, color: '#DDA0DD' }
])
</script>

<style scoped>
.rose-chart-demo {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h1 {
  color: #333;
  margin-bottom: 10px;
}

.demo-header p {
  color: #666;
  font-size: 16px;
}

.control-panel {
  margin-bottom: 20px;
}

.color-preview {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-block {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.usage-guide h4 {
  color: #333;
  margin-top: 20px;
  margin-bottom: 10px;
}

.usage-guide pre {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #007bff;
  overflow-x: auto;
}

.usage-guide ul {
  padding-left: 20px;
}

.usage-guide li {
  margin-bottom: 8px;
  line-height: 1.6;
}
</style>
