<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="qidian.it.mybatisplus.mapper.ProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="qidian.it.mybatisplus.entity.Product">
        <id column="product_id" property="productId" />
        <result column="product_name" property="productName" />
        <result column="price" property="price" />
        <result column="storage_num" property="storageNum" />
        <result column="description" property="description" />
        <result column="product_images" property="productImages" />
    </resultMap>

</mapper>
