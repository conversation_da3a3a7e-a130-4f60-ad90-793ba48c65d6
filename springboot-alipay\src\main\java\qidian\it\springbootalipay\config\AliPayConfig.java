package qidian.it.springbootalipay.config;

public class AliPayConfig {

    //APPID对应支付宝账号
    public static String APP_ID="9021000128686003";
    //应用私钥
    public static String MERCHANT_PRIVATE_KEY="MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCXi2c6AmeOHA6BZP42juvCf7LVZt2k7OMhbMVv43H2aHMfpl+C2r+YLGkXGBgljkiYKZK/Y2EPIBJ6rnPd6tWjjIAkoJDTLPgixH9tNbMtpuhJiXN3IROD0uQWZX/8rXd1wWRXvgHZxG7knthan/+HZXhsxyqefBZw+C1LrI59KgGkWX2UEbstjXfvIy05ZtyhAvwVL5A2206Bc6tQcbAFjHKEHxV9ddplz2X+YXLFChmvFrU077zyVFtypYBoa2aHjjO9vEpVnQobkDixcOYyDR3i72agk7dc7c+zcJuBHYefQh2eOAHk8SOA/6pxK18QL5mm8e/n/eTIbwPYWFpNAgMBAAECggEAfeoZIezfiAct0jtMsZ48q4WY3JE0thURfzM2NQpBJWCRzwlYsIv6x4CW7NZbMdEv5iYcxSLYgU2/rOmIYin9yn8x9bznuF7T6WdlDSOiNdtJg9hN9Pypj01JsIYzOooTCOdf7Tt1pC//h7unvlzUXvZjgdZWRgqKRuEeGZNNbRaQtZ0kfR8gcx6RwGsyPWWujLXyj6pvt//dMp214GH7lm/kRCSKDwFPhBMomLDhmyyaIEXB1UgP1uD8SH9VjMQkRwNbYwrtlY++NqlYS4EX9KqQ6bbFdMYLGlYO1tDkwvIO+4q9l+5AbofKAGndL2tkLAQ3HNd4S2pe2i3NpoMbIQKBgQDhPp8rTFaV9iYI8Y1QWtxKU6tzuBqyglyHTe/8Ipnc8IWubcgoNhSCFOcfSEtu6SpLAWOkaK/5FvcJjkS0fDXg42Q0k9kOFLZVUbpVFl+w8uJSbByTC8zrKur1XwF2yXtaSipKGHAhVZdR5lTiQJqbTPn23DCMCffRXpKYJoTxBQKBgQCsPJxlqsjaCDPvpEoDeT4iHihGHo8US2ZK9l1v17rfwnNUb5XVhBFP8PNZbYiDji33Dv+II+Vc+Bvu9KRHYhOtWjlDwilM9atfQ9+9D3qh7Yk3u9vjyedTl1BlMo2ZtvUVuqISdeCCo2yj6UhtjU4sNGpAlOAydQbEArm4ntymqQKBgEVDlXyiD+ozdETBytvP5PfyPMHr9kG4dq2g2NBm73uSdvlJhwhegPXzaZMkDxY/GyNPfQ2cp6hoWHUWy6fYnlTWeAT3BgMfeWZJKIxrwedO+SuxVSQet9gFLZVgrrmRgZ4lOlXop+qIQG0N8EJW7XQeOquf0Env8sXuWBPznGPlAoGAafF+YisDENoq2CU+QZS6W1Ihg8AYUmtuqH2SS9Fu6WQ6fJ2GlKd/3qRfi4qMhh9WtrlLBPrPgrg6tmlVA3OYlvEAeGVd8jDLEPY3EZwL2r65wMaVIzB2Ujyo/xZxczPO3WbHeoN3beQIV2qPWpOomhhDoyXwIhlJ6BelFaLiJsECgYBTdXO2bd1xHCAcTpMYO/1ZybeE+MkItJIEMftBZ4TggXR67jEa3uMpJd2lOJ8AXhc0iMcxGbv85N1/iTLxKOQlA4tNwlMAba4YuFYcBX++aqJzfdLrGTjPRbfYji/Ldk8ct86/9VI6P0aP0+D9JrUlyzMNImdxsdb3RkRTlXY6hg==";
    //支付宝公钥
    public static String ALIPAY_PUBLIC_KEY="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAk2boektKO0ksLJjaCP4kmO0ENBIX6rWPtlC98BLQDANOO+c/SeAAEiImRG96c0hFDaqnJQJUgk1kdBbG6Vrs7THQXiLbfo+up5k1UQSe8PxgVlYmqSg1hTLVyhedkST6W2+/qYSiVQutrJFGxsd/p7VCZkLScKktAWXmo8mfv2VTAWkmlnOKjO8yjt63L+j+YVamahdDAoeAlTEjsugKTNC0LnEFc5A319QtQ0JllYNYZVBFXWbY+bW8V8pHLFoznkdbEmOvZDtfM2RVTiIHhHXIsvNW/IJCeKr5Karf11yC4o9jGnIAi4Qyn14dNCR833XEmYrLdX3ScfIFWDrxAQIDAQAB";
    //这个是支付成功后要跳转到哪里的页面  success是我控制层里面的路径  改成自己的  也可以是一个页面.html
    public static String NOTIFY_URL="http://localhost:8082/notify";
    //页面跳转同步通知页面路径 需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问  和上面一样
    public static String RETURN_URL="http://localhost:5173/success";
    //签名方式   这个不用改
    public static String SIGN_TYPE="RSA2";
    //字符编码格式  不用改
    public static String CHARSET="utf-8";
    //支付宝网关(沙盒环境)  不用改
    public static String GATEWAY_URL="https://openapi-sandbox.dl.alipaydev.com/gateway.do";
    //  https://openapi.alipaydev.com/gateway.do
}
