package qidian.it.mybatisplus.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import qidian.it.mybatisplus.entity.Admin;
import qidian.it.mybatisplus.mapper.AdminMapper;
import qidian.it.mybatisplus.service.IAdminService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import qidian.it.mybatisplus.util.Result;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
public class AdminServiceImpl extends ServiceImpl<AdminMapper, Admin> implements IAdminService {

    @Override
    public Admin selectByName(String name) {
        LambdaQueryWrapper<Admin> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Admin::getUsername, name);
        return baseMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    public List<Admin> selectAll(long current) {
        QueryWrapper<Admin> queryWrapper = new QueryWrapper<>();
        IPage<Admin> page = new Page<>(current, 1);
        return baseMapper.selectPage(page, queryWrapper).getRecords();
    }

    @Override
    public Result register(Admin admin) {
        LambdaQueryWrapper<Admin> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        Admin existingAdmin = baseMapper.selectOne(lambdaQueryWrapper.eq(Admin::getUsername, admin.getUsername()));
        if (Objects.isNull(existingAdmin)) {
            if (baseMapper.insert(admin) > 0) {
                return Result.success("注册成功");
            } else {
                return Result.fail("服务器繁忙");
            }
        }
        return Result.fail("用户名已存在");
    }

    @Override
    public Result login(String username, String password) {
        // 参数校验
        if (username == null || username.trim().isEmpty()) {
            return Result.fail("用户名不能为空");
        }
        if (password == null || password.trim().isEmpty()) {
            return Result.fail("密码不能为空");
        }

        // 根据用户名查询用户
        LambdaQueryWrapper<Admin> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Admin::getUsername, username);
        Admin admin = baseMapper.selectOne(lambdaQueryWrapper);

        // 用户不存在
        if (Objects.isNull(admin)) {
            return Result.fail("用户名或密码错误");
        }

        // 密码验证
        if (!password.equals(admin.getPassword())) {
            return Result.fail("用户名或密码错误");
        }

        // 登录成功，返回用户信息（不包含密码）
        Admin loginAdmin = new Admin();
        loginAdmin.setId(admin.getId());
        loginAdmin.setUsername(admin.getUsername());
        loginAdmin.setEmail(admin.getEmail());

        return Result.success("登录成功", loginAdmin);
    }

    @Override
    public Result checkLogin(String username) {
        // 参数校验
        if (username == null || username.trim().isEmpty()) {
            return Result.fail("用户名不能为空");
        }

        // 根据用户名查询用户
        LambdaQueryWrapper<Admin> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Admin::getUsername, username);
        Admin admin = baseMapper.selectOne(lambdaQueryWrapper);

        // 检查用户是否存在
        if (Objects.isNull(admin)) {
            return Result.success("用户不存在", false);
        }

        // 用户存在，返回登录状态为true
        return Result.success("用户已登录", true);
    }
}
