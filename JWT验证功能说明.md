# JWT登录验证功能说明

## 功能概述
已完成基于JWT（JSON Web Token）的登录验证功能，使用自定义注解 `@RequiredToken` 和AOP切面来实现token验证。

## 核心组件

### 1. JWT配置类 (`JwtConfig.java`)
- **功能**: JWT token的生成、解析和验证
- **配置**: 在 `application.yml` 中配置密钥、过期时间等
- **主要方法**:
  - `createToken(String subject)`: 生成JWT token
  - `getTokenClaim(String token)`: 解析token获取Claims
  - `isTokenExpired(String token)`: 检查token是否过期
  - `getUsernameFromToken(String token)`: 从token中获取用户名

### 2. 自定义注解 (`@RequiredToken`)
- **功能**: 标记需要JWT验证的方法
- **使用**: 在Controller方法上添加此注解即可启用JWT验证

### 3. AOP切面 (`TokenAspect.java`)
- **功能**: 拦截带有 `@RequiredToken` 注解的方法，进行token验证
- **验证流程**:
  1. 从请求头中获取token
  2. 验证token是否为空
  3. 解析token并检查是否过期
  4. 验证通过则执行目标方法，否则返回错误

## 已配置的验证接口

### 需要Token验证的接口:
- `GET /admin/selectAll` - 获取所有管理员（需要token）
- `GET /admin/checkLogin` - 检查登录状态（需要token）

### 无需Token验证的接口:
- `POST /admin/login` - 用户登录（返回token）
- `POST /admin/register` - 用户注册

## 前端集成

### 1. API请求拦截器
- 自动在请求头中添加token: `headers['token'] = token`
- 从localStorage中获取保存的token

### 2. 登录流程
1. 用户输入用户名密码
2. 调用登录接口
3. 后端验证成功后返回JWT token
4. 前端保存token到localStorage
5. 后续请求自动携带token

### 3. 路由守卫
- 登录失效时自动清除token
- 跳转到登录页面

## 测试方法

### 方法1: 使用测试页面
1. 打开 `test-jwt.html` 页面
2. 使用测试账号登录: `bcd` / `qwdz4822`
3. 获取JWT token
4. 测试受保护的API接口
5. 观察后端控制台的AOP日志

### 方法2: 使用Vue前端
1. 启动前端项目: `npm run dev`
2. 登录系统，token会自动保存
3. 访问需要验证的页面，会自动携带token

### 方法3: 手动API测试
```bash
# 1. 登录获取token
curl -X POST http://localhost:8082/admin/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=bcd&password=qwdz4822"

# 2. 使用token访问受保护接口
curl -X GET http://localhost:8082/admin/selectAll \
  -H "token: YOUR_JWT_TOKEN_HERE"

# 3. 无token访问（应该失败）
curl -X GET http://localhost:8082/admin/selectAll
```

## JWT配置参数

在 `application.yml` 中的配置:
```yaml
config:
  jwt:
    secret: [长密钥字符串]  # JWT签名密钥
    expire: 1000           # token过期时间（秒）
    header: token          # 请求头名称
```

## AOP日志输出

当访问带有 `@RequiredToken` 注解的方法时，控制台会输出:
```
我是前置通知,即将调用selectAll参数是[1]
前端传过来的token===>eyJhbGciOiJIUzUxMiJ9...
我是后置通知
我是返回通知,方法返回值是Result{code=200, message='success', data=[...]}
```

## 错误处理

### Token验证失败的情况:
1. **Token为空**: 返回 `{"code": 500, "message": "token不能为空"}`
2. **Token过期**: 返回 `{"code": 500, "message": "token已失效"}`
3. **Token格式错误**: 返回 `{"code": 500, "message": "token已失效"}`

## 安全特性

1. **Token过期机制**: 防止token被长期滥用
2. **签名验证**: 防止token被篡改
3. **请求头传输**: 相比URL参数更安全
4. **AOP统一处理**: 避免在每个方法中重复验证代码

## 扩展使用

### 为新接口添加JWT验证:
```java
@RequestMapping("/newAPI")
@RequiredToken  // 添加这个注解即可
public Result newAPI() {
    // 方法实现
}
```

### 获取当前登录用户:
在AOP切面中，可以从token中提取用户信息并传递给业务方法。

## 总结

JWT验证功能已完全集成到系统中：
- ✅ 后端JWT生成和验证
- ✅ 自定义注解和AOP切面
- ✅ 前端token存储和自动携带
- ✅ 路由守卫集成
- ✅ 完整的测试工具

现在系统具备了完整的JWT身份验证能力，可以有效保护需要登录才能访问的接口。
