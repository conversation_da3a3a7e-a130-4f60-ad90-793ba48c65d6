<template>
  <div class="enterprise-layout">
    <el-container class="layout-container">
      <el-header class="top-header">
        <div class="header-content">
          <div class="logo-section">
            <el-icon class="logo-icon"><OfficeBuilding /></el-icon>
            <h1 class="system-title">企业管理系统</h1>
          </div>
          <div class="user-section">
            <el-dropdown @command="handleUserAction">
              <span class="user-info">
                <el-icon><User /></el-icon>
                <span class="username">{{ currentUser.username || '管理员' }}</span>
                <el-icon class="arrow-down"><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                  <el-dropdown-item command="settings">系统设置</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>

      <el-container class="main-container">
        <el-aside class="sidebar">
          <Aside />
        </el-aside>
        <el-main class="content-area">
          <div class="page-wrapper">
            <RouterView v-if="$route.path !== '/'" />
            <Dashboard v-else />
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
/**
 * Main Layout Component
 * <AUTHOR>
 * @description Enterprise-grade layout with modern header and navigation
 */

import { ref, onMounted } from 'vue'
import { RouterView, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { get } from '../api/api.js'
import Aside from '../components/Aside.vue'
import Dashboard from './Dashboard.vue'

const router = useRouter()
const currentUser = ref({})

// 首页需要判断用户登录状态是否过期 (已禁用强制登录)
onMounted(async () => {
  try {
    // 获取用户信息
    const adminData = localStorage.getItem('admin')
    if (adminData) {
      currentUser.value = JSON.parse(adminData)
    }

    // 检查服务器端登录状态 (已禁用)
    // const username = localStorage.getItem('userStatus')
    // if (username) {
    //   const data = await get('/admin/checkLogin', { username })
    //   console.log('登录状态检查结果:', data)

    //   if (!data.data) {
    //     // 登录已失效，清除本地存储并跳转到登录页
    //     localStorage.removeItem('userStatus')
    //     localStorage.removeItem('admin')
    //     ElMessage.warning('登录已过期，请重新登录')
    //     router.push('/login')
    //   }
    // } else {
    //   // 没有用户状态，直接跳转到登录页
    //   router.push('/login')
    // }
  } catch (error) {
    console.error('检查登录状态失败:', error)
    // 检查失败时也跳转到登录页 (已禁用)
    // localStorage.removeItem('userStatus')
    // localStorage.removeItem('admin')
    // ElMessage.error('登录状态检查失败，请重新登录')
    // router.push('/login')
  }
})

// Handle user dropdown actions
const handleUserAction = (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人资料功能开发中...')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中...')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// Handle user logout
const handleLogout = () => {
  localStorage.removeItem('admin')
  localStorage.removeItem('userStatus')
  ElMessage.success('已安全退出系统')
  router.replace('/login')
}
</script>

<style scoped>
/**
 * Enterprise Layout Styling
 * Modern glassmorphism design with professional color scheme
 */

.enterprise-layout {
  height: 100vh;
  background: #f5f5f5;
  overflow: hidden;
}

.layout-container {
  height: 100%;
}

.top-header {
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  padding: 0;
  height: 60px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 30px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo-icon {
  font-size: 24px;
  color: #409eff;
}

.system-title {
  color: #333;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.user-section {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #333;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 4px;
  background: #f5f5f5;
  border: 1px solid #e6e6e6;
  transition: all 0.3s ease;
}

.user-info:hover {
  background: #e6e6e6;
}

.username {
  font-weight: 500;
  font-size: 14px;
}

.arrow-down {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.main-container {
  height: calc(100vh - 60px);
}

.sidebar {
  width: 200px;
  background: #fff;
  border-right: 1px solid #e6e6e6;
  overflow-y: auto;
}

.content-area {
  flex: 1;
  padding: 0;
  background: #f5f5f5;
  overflow-y: auto;
}

.page-wrapper {
  height: 100%;
  background: #fff;
  margin: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e6e6e6;
  overflow-y: auto;
}

/* Global body styling */
:global(body) {
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}
</style>