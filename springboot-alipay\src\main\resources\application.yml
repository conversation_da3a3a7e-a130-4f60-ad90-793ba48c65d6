server:
  port: 8082

spring:
  application:
    name: springboot
  datasource:
    username: root
    password: root1234
    url: ***********************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-timeout: 6000
      maximum-pool-size: 5


mybatis:
  # 指定 mapper.xml 的位置
  mapper-locations: classpath:mapping/*.xml
  #扫描实体类的位置,在此处指明扫描实体类的包，在 mapper.xml 中就可以不写实体类的全路径名
  type-aliases-package: entity
  configuration:
    #默认开启驼峰命名法，可以不用设置该属性
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


