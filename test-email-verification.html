<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱验证码测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="email"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>邮箱验证码功能测试</h1>
        
        <div class="info result">
测试邮箱: <EMAIL>
授权码: kncpckscdzembjch
        </div>

        <h2>步骤1: 获取验证码</h2>
        <form id="codeForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" value="testuser123">
            </div>
            <div class="form-group">
                <label for="email">邮箱:</label>
                <input type="email" id="email" name="email" value="<EMAIL>">
            </div>
            <button type="button" id="getCodeBtn" onclick="getVerificationCode()">获取验证码</button>
        </form>

        <h2>步骤2: 验证验证码</h2>
        <form id="verifyForm">
            <div class="form-group">
                <label for="code">验证码:</label>
                <input type="text" id="code" name="code" placeholder="请输入6位验证码">
            </div>
            <button type="button" onclick="checkCode()">验证验证码</button>
            <button type="button" onclick="checkCodeExpire()">检查验证码是否过期</button>
        </form>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8082';
        let countdown = 0;
        let countdownInterval = null;

        function showResult(data, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result ' + (isError ? 'error' : 'success');
            resultDiv.textContent = JSON.stringify(data, null, 2);
        }

        // 获取验证码
        async function getVerificationCode() {
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            
            if (!username || !email) {
                showResult({ error: '请填写用户名和邮箱' }, true);
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/admin/getCode?username=${username}&email=${email}`);
                const data = await response.json();
                
                if (data.code === 200) {
                    localStorage.setItem('code', data.message);
                    showResult({
                        message: '验证码发送成功',
                        code: data.message,
                        note: '验证码已保存到localStorage，实际项目中不应该返回验证码'
                    });
                    
                    // 开始倒计时
                    startCountdown();
                } else {
                    showResult({
                        error: '验证码发送失败',
                        response: data
                    }, true);
                }
            } catch (error) {
                showResult({
                    error: error.message,
                    details: '请确保后端服务已启动'
                }, true);
            }
        }

        // 开始倒计时
        function startCountdown() {
            const btn = document.getElementById('getCodeBtn');
            countdown = 60;
            btn.disabled = true;
            
            countdownInterval = setInterval(() => {
                countdown--;
                btn.textContent = `再次获取(${countdown}s)`;
                
                if (countdown === 0) {
                    clearInterval(countdownInterval);
                    btn.disabled = false;
                    btn.textContent = '获取验证码';
                }
            }, 1000);
        }

        // 验证验证码
        function checkCode() {
            const inputCode = document.getElementById('code').value;
            const storedCode = localStorage.getItem('code');
            
            if (!inputCode) {
                showResult({ error: '请输入验证码' }, true);
                return;
            }
            
            if (inputCode === storedCode) {
                showResult({
                    message: '验证码验证成功',
                    inputCode: inputCode,
                    storedCode: storedCode
                });
            } else {
                showResult({
                    error: '验证码错误',
                    inputCode: inputCode,
                    storedCode: storedCode
                }, true);
            }
        }

        // 检查验证码是否过期
        async function checkCodeExpire() {
            const username = document.getElementById('username').value;
            
            if (!username) {
                showResult({ error: '请输入用户名' }, true);
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/admin/checkCodeExpire?username=${username}`);
                const data = await response.json();
                
                showResult({
                    message: '验证码过期检查结果',
                    status: response.status,
                    data: data
                });
            } catch (error) {
                showResult({
                    error: error.message,
                    details: '请确保后端服务已启动'
                }, true);
            }
        }

        // 页面加载时的说明
        window.onload = function() {
            showResult({
                message: '邮箱验证码功能测试说明',
                steps: [
                    '1. 填写用户名和邮箱地址',
                    '2. 点击"获取验证码"按钮',
                    '3. 检查邮箱接收验证码',
                    '4. 输入验证码进行验证',
                    '5. 检查验证码是否过期'
                ],
                note: '验证码会发送到 <EMAIL> 邮箱'
            });
        }
    </script>
</body>
</html>
