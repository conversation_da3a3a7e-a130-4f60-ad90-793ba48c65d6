<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="qidian.it.mybatisplus.mapper.EmployeeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="qidian.it.mybatisplus.entity.Employee">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="gender" property="gender" />
        <result column="age" property="age" />
        <result column="phone" property="phone" />
        <result column="email" property="email" />
        <result column="department_id" property="departmentId" />
        <result column="position" property="position" />
        <result column="hire_date" property="hireDate" />
        <result column="status" property="status" />
    </resultMap>

</mapper>
