-- 数据库初始化脚本
-- 创建basic数据库和admin表，并插入测试数据

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `basic` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `basic`;

-- 创建admin表
CREATE TABLE IF NOT EXISTS `admin` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码（加密存储）',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 插入测试用户数据
INSERT INTO `admin` (`username`, `password`, `email`) VALUES
('admin', '123456', '<EMAIL>'),
('test', 'test123', '<EMAIL>'),
('user1', 'password', '<EMAIL>')
ON DUPLICATE KEY UPDATE 
`password` = VALUES(`password`),
`email` = VALUES(`email`);

-- 创建employee表（员工表）
CREATE TABLE IF NOT EXISTS `employee` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '员工ID',
  `name` varchar(50) NOT NULL COMMENT '员工姓名',
  `gender` varchar(10) DEFAULT NULL COMMENT '性别',
  `age` int(11) DEFAULT NULL COMMENT '年龄',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系方式',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `department_id` bigint(20) DEFAULT NULL COMMENT '所属部门ID',
  `position` varchar(50) DEFAULT NULL COMMENT '职位',
  `hire_date` date DEFAULT NULL COMMENT '入职日期',
  `status` int(11) DEFAULT '1' COMMENT '在职状态（1：在职，0：离职）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工表';

-- 创建department表（部门表）
CREATE TABLE IF NOT EXISTS `department` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `name` varchar(50) NOT NULL COMMENT '部门名称',
  `description` varchar(255) DEFAULT NULL COMMENT '部门描述',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门表';

-- 插入部门测试数据
INSERT INTO `department` (`name`, `description`) VALUES
('技术部', '负责产品研发和技术支持'),
('销售部', '负责产品销售和客户关系维护'),
('市场部', '负责市场推广和品牌建设'),
('人事部', '负责人力资源管理'),
('财务部', '负责财务管理和会计核算'),
('运营部', '负责日常运营管理'),
('客服部', '负责客户服务和售后支持'),
('行政部', '负责行政管理和后勤保障')
ON DUPLICATE KEY UPDATE 
`description` = VALUES(`description`);

-- 插入员工测试数据
INSERT INTO `employee` (`name`, `gender`, `age`, `phone`, `email`, `department_id`, `position`, `hire_date`, `status`) VALUES
('张三', '男', 28, '13800138001', '<EMAIL>', 1, '高级工程师', '2023-01-15', 1),
('李四', '女', 25, '13800138002', '<EMAIL>', 1, '前端工程师', '2023-03-20', 1),
('王五', '男', 32, '13800138003', '<EMAIL>', 2, '销售经理', '2022-08-10', 1),
('赵六', '女', 29, '13800138004', '<EMAIL>', 3, '市场专员', '2023-05-12', 1),
('钱七', '男', 35, '13800138005', '<EMAIL>', 4, 'HR主管', '2022-12-01', 1),
('孙八', '女', 27, '13800138006', '<EMAIL>', 5, '会计', '2023-02-28', 1),
('周九', '男', 30, '13800138007', '<EMAIL>', 6, '运营专员', '2023-04-15', 1),
('吴十', '女', 26, '13800138008', '<EMAIL>', 7, '客服代表', '2023-06-01', 1)
ON DUPLICATE KEY UPDATE 
`gender` = VALUES(`gender`),
`age` = VALUES(`age`),
`phone` = VALUES(`phone`),
`email` = VALUES(`email`),
`department_id` = VALUES(`department_id`),
`position` = VALUES(`position`),
`hire_date` = VALUES(`hire_date`),
`status` = VALUES(`status`);

-- 查询验证数据
SELECT '=== 管理员数据 ===' as info;
SELECT * FROM admin;

SELECT '=== 部门数据 ===' as info;
SELECT * FROM department;

SELECT '=== 员工数据 ===' as info;
SELECT * FROM employee;

-- 显示测试账号信息
SELECT '=== 测试账号信息 ===' as info;
SELECT 
    '可用的测试账号：' as '说明',
    'admin/123456, test/test123, user1/password' as '用户名/密码';
