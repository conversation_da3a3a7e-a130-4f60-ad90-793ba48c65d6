package qidian.it.mybatisplus.interceptor;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import qidian.it.mybatisplus.util.Result;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.Objects;

@Component
public class MyInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        System.out.println("preHandle");
        response.setCharacterEncoding("GBK");
        //根据条件来觉得返回值为true或者false
        //1.在当前拦截器中获取请求参数(用户名)
        //2.根据用户名是否以admin开头,返回true或者false

        //获取username请求参数
       String username= request.getParameter("username");

       if(Objects.nonNull(username)){
           if(username.startsWith("admin")){
               return true;
           }else{
             PrintWriter printWriter= response.getWriter();
               printWriter.println(JSONObject.parse(JSON.toJSONString(Result.fail("该用户没有权限访问"))));
               printWriter.flush();

               return false;
           }
       }

        return false;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        System.out.println("postHandle");

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        System.out.println("afterCompletion");
    }



}
