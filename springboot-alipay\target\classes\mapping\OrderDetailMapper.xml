<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="qidian.it.springbootalipay.mapper.OrderDetailMapper" >
  <resultMap id="BaseResultMap" type="qidian.it.springbootalipay.entity.OrderDetail" >
    <id column="detail_id" property="detailId" jdbcType="INTEGER" />
    <result column="order_id" property="orderId" jdbcType="INTEGER" />
    <result column="product_id" property="productId" jdbcType="INTEGER" />
    <result column="product_name" property="productName" jdbcType="VARCHAR" />
    <result column="price" property="price" jdbcType="DOUBLE" />
    <result column="quantity" property="quantity" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    detail_id, order_id, product_id, product_name, price, quantity
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from order_detail
    where detail_id = #{detailId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from order_detail
    where detail_id = #{detailId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="qidian.it.springbootalipay.entity.OrderDetail" >
    insert into order_detail (detail_id, order_id, product_id, 
      product_name, price, quantity
      )
    values (#{detailId,jdbcType=INTEGER}, #{orderId,jdbcType=INTEGER}, #{productId,jdbcType=INTEGER}, 
      #{productName,jdbcType=VARCHAR}, #{price,jdbcType=DOUBLE}, #{quantity,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="qidian.it.springbootalipay.entity.OrderDetail" >
    insert into order_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="detailId != null" >
        detail_id,
      </if>
      <if test="orderId != null" >
        order_id,
      </if>
      <if test="productId != null" >
        product_id,
      </if>
      <if test="productName != null" >
        product_name,
      </if>
      <if test="price != null" >
        price,
      </if>
      <if test="quantity != null" >
        quantity,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="detailId != null" >
        #{detailId,jdbcType=INTEGER},
      </if>
      <if test="orderId != null" >
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="productId != null" >
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="productName != null" >
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        #{price,jdbcType=DOUBLE},
      </if>
      <if test="quantity != null" >
        #{quantity,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="qidian.it.springbootalipay.entity.OrderDetail" >
    update order_detail
    <set >
      <if test="orderId != null" >
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="productId != null" >
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="productName != null" >
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        price = #{price,jdbcType=DOUBLE},
      </if>
      <if test="quantity != null" >
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
    </set>
    where detail_id = #{detailId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="qidian.it.springbootalipay.entity.OrderDetail" >
    update order_detail
    set order_id = #{orderId,jdbcType=INTEGER},
      product_id = #{productId,jdbcType=INTEGER},
      product_name = #{productName,jdbcType=VARCHAR},
      price = #{price,jdbcType=DOUBLE},
      quantity = #{quantity,jdbcType=INTEGER}
    where detail_id = #{detailId,jdbcType=INTEGER}
  </update>
</mapper>