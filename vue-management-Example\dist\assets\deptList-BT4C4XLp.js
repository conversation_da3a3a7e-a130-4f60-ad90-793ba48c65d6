import{g as y}from"./api-BgzO6Ksg.js";import{r as u,h as E,i as O,c as q,f as r,j as A,b as t,w as a,a as d,k as G,l as H,F as J,o as $,e as m,t as C,E as f}from"./index-6989FsBU.js";const K={style:{display:"flex","align-items":"center"}},Q={style:{"margin-left":"10px"}},R={class:"dialog-footer"},W={class:"dialog-footer"},h={__name:"deptList",setup(X){const x=u(!0),p=u(!1),_=u(!1),V=u(1),k=u(0),n=E({}),i=E({});u("");const N=()=>{_.value=!0},B=async()=>{console.log(i);const o=await y("/addDept",i);o.code==200?(_.value=!1,g(),f({title:"Success",message:o.message,type:"success"})):f({title:"Error",message:o.message,type:"error"})},S=o=>{V.value=o,g()},z=(o,e)=>{p.value=!0,Object.assign(n,e)},P=()=>{j()},j=async()=>{const o=await y("/updateDept",n);o.code==200?(g(),p.value=!1,f({title:"Success",message:o.message,type:"success"})):f({title:"Error",message:o.message,type:"error"})},F=async(o,e)=>{const s=await y("/deleteDept",{id:e.id});s.code==200?(w.value.splice(o,1),g(),p.value=!1,f({title:"Success",message:s.message,type:"success"})):f({title:"Error",message:s.message,type:"error"})},w=u([]),g=async()=>{const o=await y("/getDeptInfo",{currentPage:V.value});w.value=o.data,k.value=o.code,setTimeout(()=>{x.value=!1},300)};return O(async()=>{g()}),(o,e)=>{const s=d("el-button"),b=d("el-table-column"),T=d("el-table"),I=d("el-pagination"),v=d("el-input"),c=d("el-form-item"),D=d("el-form"),U=d("el-dialog"),L=G("loading");return $(),q(J,null,[r("div",null,[t(s,{type:"warning",onClick:N},{default:a(()=>e[10]||(e[10]=[m("添加部门",-1)])),_:1,__:[10]})]),A(($(),H(T,{data:w.value,style:{width:"90%"}},{default:a(()=>[t(b,{label:"部门编号",width:"120"},{default:a(l=>[r("div",K,[r("span",Q,C(l.row.id),1)])]),_:1}),t(b,{label:"部门名称",width:"120"},{default:a(l=>[r("div",null,C(l.row.name),1)]),_:1}),t(b,{label:"部门描述",width:"120"},{default:a(l=>[r("div",null,C(l.row.description),1)]),_:1}),t(b,{label:"操作"},{default:a(l=>[t(s,{size:"small",onClick:M=>z(l.$index,l.row)},{default:a(()=>e[11]||(e[11]=[m(" 编辑 ",-1)])),_:2,__:[11]},1032,["onClick"]),t(s,{size:"small",type:"danger",onClick:M=>F(l.$index,l.row)},{default:a(()=>e[12]||(e[12]=[m(" 删除 ",-1)])),_:2,__:[12]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[L,x.value]]),t(I,{"current-page":V.value,"onUpdate:currentPage":e[0]||(e[0]=l=>V.value=l),"page-size":2,layout:"prev, pager, next",total:k.value,onCurrentChange:S},null,8,["current-page","total"]),t(U,{modelValue:p.value,"onUpdate:modelValue":e[5]||(e[5]=l=>p.value=l),title:"员工添加",width:"500"},{footer:a(()=>[r("div",R,[t(s,{onClick:e[4]||(e[4]=l=>p.value=!1)},{default:a(()=>e[13]||(e[13]=[m("取消",-1)])),_:1,__:[13]}),t(s,{type:"primary",onClick:P},{default:a(()=>e[14]||(e[14]=[m(" 确认 ",-1)])),_:1,__:[14]})])]),default:a(()=>[t(D,{model:n,"label-width":"auto",style:{"max-width":"600px"}},{default:a(()=>[t(c,{label:"部门编号"},{default:a(()=>[t(v,{modelValue:n.id,"onUpdate:modelValue":e[1]||(e[1]=l=>n.id=l),disabled:""},null,8,["modelValue"])]),_:1}),t(c,{label:"部门名称"},{default:a(()=>[t(v,{modelValue:n.name,"onUpdate:modelValue":e[2]||(e[2]=l=>n.name=l)},null,8,["modelValue"])]),_:1}),t(c,{label:"部门描述"},{default:a(()=>[t(v,{modelValue:n.description,"onUpdate:modelValue":e[3]||(e[3]=l=>n.description=l)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(U,{modelValue:_.value,"onUpdate:modelValue":e[9]||(e[9]=l=>_.value=l),title:"用户修改",width:"500"},{footer:a(()=>[r("div",W,[t(s,{onClick:e[8]||(e[8]=l=>_.value=!1)},{default:a(()=>e[15]||(e[15]=[m("取消",-1)])),_:1,__:[15]}),t(s,{type:"primary",onClick:B},{default:a(()=>e[16]||(e[16]=[m(" 确认 ",-1)])),_:1,__:[16]})])]),default:a(()=>[t(D,{model:i,"label-width":"auto",style:{"max-width":"600px"}},{default:a(()=>[t(c,{label:"部门名称"},{default:a(()=>[t(v,{modelValue:i.name,"onUpdate:modelValue":e[6]||(e[6]=l=>i.name=l)},null,8,["modelValue"])]),_:1}),t(c,{label:"部门描述"},{default:a(()=>[t(v,{modelValue:i.description,"onUpdate:modelValue":e[7]||(e[7]=l=>i.description=l)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])],64)}}};export{h as default};
