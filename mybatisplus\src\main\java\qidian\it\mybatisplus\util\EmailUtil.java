package qidian.it.mybatisplus.util;

import com.sun.mail.util.MailSSLSocketFactory;
import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.security.GeneralSecurityException;
import java.util.Properties;
import java.util.Random;

public class EmailUtil {
    public static String sendMail(String to) {
        Random random = new Random();
        String code = String.valueOf(random.nextInt(100001) + 899999);
        
        // 设置发送邮件的主机 smtp.qq.com
        String host = "smtp.qq.com";

        // 1.创建连接对象，连接到邮箱服务器
        Properties props = System.getProperties();

        // 设置邮件服务器
        props.setProperty("mail.smtp.host", host);
        props.put("mail.smtp.auth", "true");

        // SSL加密
        MailSSLSocketFactory sf = null;
        try {
            sf = new MailSSLSocketFactory();
        } catch (GeneralSecurityException e) {
            e.printStackTrace();
        }

        sf.setTrustAllHosts(true);
        props.put("mail.smtp.ssl.enable", "true");
        props.put("mail.smtp.ssl.socketFactory", sf);

        // 创建会话，使用您提供的邮箱账号
        Session session = Session.getDefaultInstance(props, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                // 使用您提供的邮箱账号和授权码
                return new PasswordAuthentication("<EMAIL>", "kncpckscdzembjch");
            }
        });

        try {
            Message message = new MimeMessage(session);

            // 2.1设置发件人
            message.setFrom(new InternetAddress("<EMAIL>"));

            // 2.2设置收件人
            message.setRecipient(MimeMessage.RecipientType.TO, new InternetAddress(to));

            // 2.3邮件的主题
            message.setSubject("用户注册验证码");

            // 2.4设置邮件的正文
            String emailContent = "<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>" +
                    "<h2 style='color: #333; text-align: center;'>用户注册验证码</h2>" +
                    "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>" +
                    "<p style='font-size: 16px; color: #555;'>您好！</p>" +
                    "<p style='font-size: 16px; color: #555;'>您正在进行用户注册，您的验证码是：</p>" +
                    "<div style='text-align: center; margin: 20px 0;'>" +
                    "<span style='font-size: 24px; font-weight: bold; color: #007bff; background: #e7f3ff; padding: 10px 20px; border-radius: 4px;'>" + code + "</span>" +
                    "</div>" +
                    "<p style='font-size: 14px; color: #666;'>验证码有效期为2分钟，请及时使用。</p>" +
                    "<p style='font-size: 14px; color: #666;'>如果这不是您的操作，请忽略此邮件。</p>" +
                    "</div>" +
                    "<div style='text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;'>" +
                    "<p style='font-size: 12px; color: #999;'>此邮件由系统自动发送，请勿回复。</p>" +
                    "</div>" +
                    "</div>";
            
            message.setContent(emailContent, "text/html;charset=UTF-8");
            
            // 3.发送邮件
            Transport.send(message);
            System.out.println("验证码邮件发送成功，验证码：" + code + "，发送到：" + to);
            return code;
        } catch (MessagingException mex) {
            mex.printStackTrace();
            System.err.println("邮件发送失败：" + mex.getMessage());
        }
        return "发送失败";
    }

    public static void main(String[] args) {
        // 测试邮件发送
        System.out.println("测试发送验证码到：<EMAIL>");
        String result = sendMail("<EMAIL>");
        System.out.println("发送结果：" + result);
    }
}
