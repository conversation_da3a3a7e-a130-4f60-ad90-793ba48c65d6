package qidian.it.springbootalipay.service.impl;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayTradeRefundRequest;
import com.alipay.api.response.AlipayTradeRefundResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qidian.it.springbootalipay.config.AliPayConfig;
import qidian.it.springbootalipay.entity.OrderDetail;
import qidian.it.springbootalipay.entity.Orders;
import qidian.it.springbootalipay.entity.Product;
import qidian.it.springbootalipay.entity.Result;
import qidian.it.springbootalipay.mapper.OrderDetailMapper;
import qidian.it.springbootalipay.mapper.OrdersMapper;
import qidian.it.springbootalipay.mapper.ProductMapper;
import qidian.it.springbootalipay.service.OrderServie;

import java.util.HashMap;
import java.util.Map;

@Service
public class OrderServieImpl implements OrderServie {

    @Autowired
    OrdersMapper ordersMapper;

    @Autowired
    OrderDetailMapper orderDetailMapper;


    @Autowired
    ProductMapper productMapper;

    @Override
    public Result createOrder(Orders orders, OrderDetail orderDetail) {

        System.out.println("orders====>>" + orders);
        System.out.println("orderDetail====>>" + orderDetail);
        orders.setUserId(1);//模拟用户编号为1
        orders.setStatus(1);//1:订单已支付 0:未支付
        if (ordersMapper.insertSelective(orders) > 0) {
            Product product = productMapper.selectByPrimaryKey(orderDetail.getProductId());
            orderDetail.setOrderId(orders.getOrderId());
            orderDetail.setQuantity(1);
            orderDetail.setProductName(product.getProductName());

            if (orderDetailMapper.insert(orderDetail) > 0) {
                product.setStorageNum(product.getStorageNum() - 1);
                productMapper.updateByPrimaryKeySelective(product);
                return Result.success("订单创建成功");
            }
            return Result.fail("订单创建失败");
        }
        return Result.fail("服务繁忙");
    }

    @Override
    public Result refund(Integer orderId) {
        Orders orders = ordersMapper.selectByPrimaryKey(orderId);
// 创建 AlipayClient 实例
        AlipayClient alipayClient = new DefaultAlipayClient(
                AliPayConfig.GATEWAY_URL,
                AliPayConfig.APP_ID,
                AliPayConfig.MERCHANT_PRIVATE_KEY,
                "json",
                AliPayConfig.CHARSET,
                AliPayConfig.ALIPAY_PUBLIC_KEY,
                AliPayConfig.SIGN_TYPE
        );
// 设置退款请求参数
        AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
        request.setBizContent("{" +
                "\"out_trade_no\":\"" + orders.getOutTradeNo() + "\"," + // 订单号
                "\"refund_amount\":\"" + orders.getTotalPrice() + "\"" + // 退款金额
                "}");
        try {
// 发送退款请求
            AlipayTradeRefundResponse response = alipayClient.execute(request);
            if (response.isSuccess()) {
                return Result.success("退款成功");
            } else {
            return Result.fail("退款失败");
            }
        } catch (AlipayApiException e) {
            return Result.fail("退款失败,异常信息:"+e.getMessage());
        }

    }

}
