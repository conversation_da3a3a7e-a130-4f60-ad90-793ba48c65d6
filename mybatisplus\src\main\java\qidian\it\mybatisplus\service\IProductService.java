package qidian.it.mybatisplus.service;

import org.springframework.web.multipart.MultipartFile;
import qidian.it.mybatisplus.entity.Product;
import com.baomidou.mybatisplus.extension.service.IService;
import qidian.it.mybatisplus.util.Result;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
public interface IProductService extends IService<Product> {
    Result addProduct(Product product, MultipartFile multipartFile);
}
