<template>
  <div class="form-container">
    <el-card class="form-card">
      <el-form :model="form" label-width="auto" class="form">
        <el-form-item label="用户名">
          <el-input v-model="form.username"></el-input>
        </el-form-item>
        
        <el-form-item label="密码">
          <el-input v-model="form.password" show-password></el-input>
        </el-form-item>

        <el-form-item label="确认密码">
          <el-input v-model="form.confirmPassword" show-password></el-input>
        </el-form-item>

       <el-form-item>
          <el-input v-model="form.code" placeholder="请输入验证码" style="width: 40%;"></el-input>
          <el-button   :disabled="flag"  @click="getCode" type="warning" style="margin-left: 10px;"ßßß>{{msg}}</el-button>
        </el-form-item>


        <el-form-item class="btn-container">
          <el-button type="primary" @click="register">确认</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref,reactive } from 'vue';
import { useRouter } from "vue-router";
import { get, post } from '../api/api.js';
import { ElNotification } from 'element-plus'
const router = useRouter();
const form = reactive({
  username: '',
   password: '',
   confirmPassword: ''
});

const msg=ref('获取验证码');
const flag=ref(false);

const getCode=async()=>{//获取验证码

const data=await get("/getCode",{username:form.username,email:"<EMAIL>"});
console.log("data==>",data);
if(data.code==200){
  localStorage.setItem("code",data.message);
  console.log('验证码',localStorage.getItem("code"));
}


const time=ref(5);
var inter=setInterval(()=>{
  time.value--;
  flag.value=true;
  msg.value="再次获取("+time.value+"s)"
  if(time.value==0){
  clearInterval(inter);
  flag.value=false;
  msg.value="获取验证码";
  }
},1000);
}

//注册
const register = async () => {
if(!form.username){
   ElNotification({
	    title: 'Error',
	    message: '用户名不能为空',
	    type: 'error',
	  });
    return;
}

if(form.password != form.confirmPassword){
   ElNotification({
	    title: 'Error',
	    message: '密码不一致',
	    type: 'error',
	  });
    return;
}



console.log("输入的验证码",form.code);

if(form.code!=localStorage.getItem("code")){
     ElNotification({
	    title: 'Error',
	    message: '验证码错误',
	    type: 'error',
	  });
    return;
}


 const data= await get("/checkCodeExpire", {username:form.username});
if(data.code==200){
    ElNotification({
	    title: 'Success',
	    message: data.message,
	    type: 'success',
	  });
}else{
    ElNotification({
	    title: 'Error',
	    message: data.message,
	    type: 'error',
	  });

    return;
}



// const data= await get("/checkCodeExpire", form);

// if(data.code==200){

// form.username='';
// form.password='';
// form.confirmPassword='';
  
//   ElNotification({
// 	    title: 'Success',
// 	    message: data.message,
// 	    type: 'success',
// 	  });
// }else{
//   ElNotification({
// 	    title: 'Error',
// 	    message: data.message,
// 	    type: 'error',
// 	  });

// }

}






</script>

<style scoped>
/* 背景图片和整体美化 */
.form-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: url('@/assets/images/login-bg.png') no-repeat center center fixed;
  background-size: cover;
  font-family: 'Arial', sans-serif; /* 优雅的字体 */
}


/* 表单卡片样式，背景半透明 */
.form-card {
  width: 600px;
  padding: 30px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.85); /* 半透明白色背景 */
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2); /* 阴影 */
  backdrop-filter: blur(10px); /* 背景模糊效果 */
  border: 1px solid rgba(0, 0, 0, 0.1); /* 边框颜色 */
}

/* 表单样式 */
.form {
  width: 100%;
  color: #333;
}

/* 输入框和按钮样式 */
.el-input, .el-button {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 每个表单项之间的间距 */
.el-form-item {
  margin-bottom: 20px;
}

/* 输入框样式 */
.el-input {
  width: 100%;
  font-size: 16px;
  padding: 12px;
  border: 1px solid #ddd;
  background-color: #f9f9f9;
  color: #555;
}

/* 按钮样式 */
.el-button {
  width: 150px;
  height: 45px;
  background-color: #4a90e2; /* 蓝色 */
  color: white;
  border: none;
  font-size: 16px;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.el-button:hover {
  background-color: #357ab7; /* 深蓝色 */
  transform: translateY(-3px); /* 按钮悬浮效果 */
}

/* 按钮容器 */
.btn-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 输入框的聚焦效果 */
.el-input.is-focus {
  border-color: #4a90e2;
  box-shadow: 0 0 5px rgba(74, 144, 226, 0.5);
}

/* 表单标签样式 */
.el-form-item__label {
  font-size: 16px;
  font-weight: bold;
  color: #444;
}

/* 美化表单容器的边框和阴影 */
.el-card {
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* 调整表单卡片间距 */
.el-form {
  margin-top: 20px;
}

/* 边框和背景颜色 */
.el-card {
  background: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
