package qidian.it.mybatisplus.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import qidian.it.mybatisplus.entity.Product;
import qidian.it.mybatisplus.service.IProductService;
import qidian.it.mybatisplus.util.FileUtil;
import qidian.it.mybatisplus.util.Result;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@RestController
@CrossOrigin
@RequestMapping("/product")
public class ProductController {
    @Autowired
    private IProductService productService;
    @RequestMapping("/addProduct")
    public Result addProduct(Product product,@RequestParam("file") MultipartFile multipartFile) {
       return productService.addProduct(product, multipartFile);
    }

    @RequestMapping("/getAllProducts")
    public List< Product> getAllProducts(){
        return productService.getAllProducts();
    }

    @RequestMapping("/download")
    public void download(HttpServletResponse resp, @RequestParam("productId") int productId) {
        productService.download(resp, productId);
    }
}
