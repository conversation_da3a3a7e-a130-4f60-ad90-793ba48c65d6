package qidian.it.springbootalipay.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qidian.it.springbootalipay.entity.Result;
import qidian.it.springbootalipay.util.EmailUtil;
import qidian.it.springbootalipay.util.RedisUtil;


@RestController
@CrossOrigin
public class UserController {

    @Autowired
    RedisUtil redisUtil;


    @RequestMapping("/getCode")
    public Result getCode(String username,String email){
        String code=EmailUtil.sendMail(email);
        //123456 =2分钟    hello,123456
        //123123=2分钟     hello 123123
        redisUtil.set(username,code);
        redisUtil.expire(username,20);
        return Result.success(code);
    }




    @RequestMapping("/checkCodeExpire")
    public Result checkCodeExpire(String username){
        if(redisUtil.get(username)==null){
            return Result.fail("验证码已过期");
        }
        return Result.success("验证成功");
    }


}
