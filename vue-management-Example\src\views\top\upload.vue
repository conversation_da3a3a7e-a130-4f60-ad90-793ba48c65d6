<template>
  <el-form :model="form" label-width="auto" style="max-width: 600px;margin:20px;">
    
   <el-form-item label="商品名称">
      <el-input v-model="form.productName" />
    </el-form-item>

  <el-form-item label="商品价格">
      <el-input v-model="form.price" />
    </el-form-item>

      <el-form-item label="商品库存">
      <el-input v-model="form.storageNum" />
    </el-form-item>


   <el-upload ref="uploadRef" class="upload-demo" drag
	action="http://localhost:8082/product/addProduct"
	multiple
	:data="form"
	name="file"
	:on-success="handleUploadSuccess"
   	:auto-upload="false">
	<!-- 	<template #trigger>
			<el-button type="primary">选择文件</el-button>
		</template> -->
		
		 <el-icon class="el-icon--upload"><upload-filled /></el-icon>
		    <div class="el-upload__text">
		      拖动文件或<em>点击上传</em>
		    </div>
		<template #tip>
		      <div class="el-upload__tip">
		      </div>
		    </template>
		<!-- <template #tip>
			<div class="el-upload__tip">
			</div>
		</template> -->
   </el-upload>
  
    <el-form-item>
      <el-button type="primary" @click="submitUpload">确认</el-button>
     <!-- <el-button>Cancel</el-button> -->
    </el-form-item>
	
  </el-form>
</template>

<script setup>
import { ref,reactive ,onMounted} from 'vue'
import { UploadFilled } from '@element-plus/icons-vue'
import { ElNotification } from 'element-plus'

const uploadRef = ref(null);
const form = reactive({
 
})

	const submitUpload = () => {
		if (uploadRef.value) {
			uploadRef.value.submit()
		}
		console.log(form.file_type_id);
	}
	
	// 上传成功后的处理
	const handleUploadSuccess = (response, file, fileList) => {
	  console.log('上传成功:', response);
	  if(response.code==200){
		  ElNotification({
		      title: 'Success',
		      message: response.msg,
		      type: 'success',
		    }) 
			
			form.file_type_id='';
		 // 清空文件选择
			if (uploadRef.value) {
			  uploadRef.value.clearFiles();  // 清空上传文件
			}
	  }else{
		ElNotification({
		    title: 'Error',
		    message: response.msg,
		    type: 'error',
		  }) 
		  // 清空文件选择
			if (uploadRef.value) {
			  uploadRef.value.clearFiles();  // 清空上传文件
			}
	}
	}
	
</script>
<style>
	.el-upload{
		width: 56%;
	}
</style>