<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="057b29e6-2810-44c6-8bc0-ebb941437e91" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\maven\apache-maven-3.6.3" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="31RIqqbcHs8PvDwJLgP4EM0wvX1" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Notification.DisplayName-DoNotAsk-DatabaseConfigFileWatcher.found&quot;: &quot;找到数据库连接形参&quot;,
    &quot;Notification.DoNotAsk-DatabaseConfigFileWatcher.found&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;Spring Boot.MybatisplusApplication.executor&quot;: &quot;Run&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/idea文件/作业/mybatisplus/src/main/java/qidian/it/mybatisplus/aspect&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.CodeGenerator (1).executor&quot;: &quot;Run&quot;,
    &quot;应用程序.CodeGenerator.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.MybatisplusApplication.executor&quot;: &quot;Debug&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\idea文件\作业\mybatisplus\src\main\java\qidian\it\mybatisplus\aspect" />
      <recent name="E:\idea文件\作业\mybatisplus\src\main\java\qidian\it\mybatisplus\config" />
      <recent name="E:\idea文件\作业\mybatisplus\src\main\java\qidian\it\mybatisplus\annotation" />
      <recent name="E:\idea文件\作业\mybatisplus\src\main\java\qidian\it\mybatisplus" />
      <recent name="E:\idea文件\mybatisplus\src\main\java\qidian\it\mybatisplus\util" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="qidian.it.mybatisplus.controller" />
      <recent name="qidian.it.mybatisplus.util" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.MybatisplusApplication">
    <configuration name="CodeGenerator (1)" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="qidian.it.mybatisplus.generator.CodeGenerator" />
      <module name="mybatisplus" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="qidian.it.mybatisplus.generator.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CodeGenerator" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="qidian.it.mybatisplus.generator.CodeGenerator" />
      <module name="mybatisplus" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="qidian.it.mybatisplus.generator.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MybatisplusApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="qidian.it.mybatisplus.MybatisplusApplication" />
      <module name="mybatisplus" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="qidian.it.mybatisplus.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MybatisplusApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="mybatisplus" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="qidian.it.mybatisplus.MybatisplusApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="qidian.it.mybatisplus.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.MybatisplusApplication" />
        <item itemvalue="应用程序.MybatisplusApplication" />
        <item itemvalue="应用程序.CodeGenerator (1)" />
        <item itemvalue="应用程序.CodeGenerator" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="057b29e6-2810-44c6-8bc0-ebb941437e91" name="更改" comment="" />
      <created>1755480652171</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755480652171</updated>
      <workItem from="1755480653401" duration="8066000" />
      <workItem from="1755565263751" duration="4495000" />
      <workItem from="1755651852288" duration="5572000" />
      <workItem from="1755667699346" duration="2458000" />
      <workItem from="1755738509682" duration="3442000" />
      <workItem from="1755742130595" duration="766000" />
      <workItem from="1755742910702" duration="555000" />
      <workItem from="1755743643236" duration="904000" />
      <workItem from="1755744569694" duration="1629000" />
      <workItem from="1755754931524" duration="2301000" />
      <workItem from="1755758664418" duration="1337000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>