<template>
  <div class="forgot">
    <el-card>
      <template #header>忘记密码</template>
      <el-form :model="form" label-width="90px" :rules="rules" ref="formRef">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.username" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" type="email" />
        </el-form-item>
        <el-form-item label="验证码" prop="code">
          <el-input v-model="form.code" placeholder="随便填(演示)" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="verify">验证</el-button>
          <el-button @click="goToLogin">返回登录</el-button>
        </el-form-item>
        <el-form-item label="新密码" v-if="verified" prop="newPassword">
          <el-input v-model="form.newPassword" show-password />
        </el-form-item>
        <el-form-item v-if="verified">
          <el-button type="success" @click="reset">重设密码</el-button>
          <el-button @click="goToLogin">返回登录</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>
<script setup>
import { reactive, ref } from 'vue'
import { get } from '../../api/api.js'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()
const form = reactive({ username:'', email:'', code:'', newPassword:'' })
const formRef = ref(null)
const verified = ref(false)
const rules = {
  username: [{required:true, message:'请输入用户名', trigger:'blur'}],
  email: [
    {required:true, message:'请输入邮箱', trigger:'blur'},
    {type:'email', message:'邮箱格式不正确', trigger:'blur'}
  ],
  code: [{required:true, message:'请输入验证码', trigger:'blur'}]
}

const verify = async () => {
  if(!formRef.value) return
  let valid = false
  try {
    valid = await formRef.value.validate()
  } catch (e) {
    valid = false
  }
  if(!valid){
    ElMessage.warning('请先完善表单信息')
    return
  }
  try {
    const res = await get('/admin/verifyAccount', { username: form.username, email: form.email })
    if(res.code===200){
      verified.value = true
      ElMessage.success('验证通过，请输入新密码')
    }else{
      ElMessage.error(res.message || '验证失败')
    }
  } catch(err){
    ElMessage.error('请求失败：' + (err?.message || '未知错误'))
  }
}

const reset = async () => {
  if(!form.newPassword){ElMessage.error('请输入新密码');return}
  try {
    const res = await get('/admin/resetPassword', { username: form.username, newPassword: form.newPassword })
    if(res.code===200){
      ElMessage.success('密码重设成功，请返回登录')
    }else{
      ElMessage.error(res.message || '重设失败')
    }
  } catch(err){
    ElMessage.error('请求失败：' + (err?.message || '未知错误'))
  }
}

// 返回登录页面
const goToLogin = () => {
  router.push('/login')
}
</script>
<style scoped>
.forgot{padding:20px; max-width:500px;margin:40px auto;}
</style>
